/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: C9BF597AD2502877B71BF2C990DD3FCBD9AACC80
--------------------------------------------------------------------
-- <PERSON><PERSON>s stattistics from one table to another
-- Should be called in Post script just before deleting *_Old table
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_CloneStatsToTable
    @tableName NVARCHAR(256),
    @newTableName NVARCHAR(256)
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(13)+CHAR(10)

    DECLARE @statName NVARCHAR(256)
    DECLARE @filter NVARCHAR(256)

    DECLARE StatsCursor CURSOR LOCAL FAST_FORWARD FOR
    SELECT   name,
          filter_definition
    FROM   sys.stats
    WHERE   object_id=OBJECT_ID(@tableName)
          AND (auto_created=1  OR user_created = 1) -- Excluding case when both are 0 which means statistics supporting index that were created when index was created
    ORDER BY stats_id
    OPEN StatsCursor
    FETCH NEXT FROM StatsCursor INTO @statName, @filter
    WHILE (@@FETCH_STATUS = 0)
    BEGIN
       DECLARE @cmd NVARCHAR(MAX) = 'CREATE STATISTICS ' + @statName + ' ON '+ @newTableName + '(' + @newLine

       SELECT
       @cmd += c.name +','
       FROM sys.stats s
       JOIN sys.stats_columns sc
       ON s.object_id=sc.object_id
       AND s.stats_id=sc.stats_id
       JOIN sys.columns c
       ON c.object_id=sc.object_id
       AND sc.column_id = c.column_id
       WHERE s.object_id=OBJECT_ID(@tableName) AND s.name = @statName
       ORDER BY stats_column_id

       SET @cmd = SUBSTRING(@cmd, 1, LEN(@cmd) - 1)

       SET @cmd  += ')' + @newLine

       IF @filter IS NOT NULL
       BEGIN
          SET @cmd = @cmd + 'WHERE ' +@filter
       END

       EXEC(@cmd)
        FETCH NEXT FROM StatsCursor INTO @statName, @filter
    END
    CLOSE StatsCursor
    DEALLOCATE StatsCursor

    RETURN 0
END

GO

