/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 874996AA5B5FD147133B6A14AAEA147497C13AAB
CREATE PROCEDURE AnalyticsInternal.prc_CloneTable
    @tableName NVARCHAR(256),
    @newTableName NVARCHAR(256),
    @partitionScheme NVARCHAR(256),
    @stripIdentityAttribute BIT NULL
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(13)+CHAR(10)

    DECLARE @cmd NVARCHAR(MAX) ='CREATE TABLE '+@newTableName + @newLine +'('

    SELECT
    @cmd += @newLine
        +CHAR(9)
        + c.name
        + ' '
        + t.name -- Keeping original name, using UPPER causes issues for turkish locale
        + CASE t.name
            WHEN 'varchar' THEN CONCAT('(', c.max_length, ')')
            WHEN 'nvarchar' THEN CONCAT('(', c.max_length/2, ')')
            WHEN 'datetimeoffset' THEN CONCAT('(', c.scale, ')')
            WHEN 'decimal' THEN CONCAT('(', c.precision,',', c.scale, ')')
            ELSE '' END
        + ' '
        + IIF (c.is_nullable = 0, 'NOT NULL', 'NULL')
        + IIF (c.is_identity = 1 AND ISNULL(@stripIdentityAttribute, 0) = 0, ' IDENTITY', '')
        + ','
    FROM sys.columns c
    JOIN sys.types t
    ON c.system_type_id=t.system_type_id
    AND c.user_type_id=t.user_type_id
    WHERE c.object_id=OBJECT_ID(@tableName)
    ORDER BY column_id

    SET @cmd = SUBSTRING(@cmd, 1, LEN(@cmd) - 1) + @newLine +')'

    SET @partitionScheme  =ISNULL(@partitionScheme, (SELECT DISTINCT ds.name  FROM    sys.indexes i  JOIN    sys.data_spaces ds on i.data_space_id = ds.data_space_id WHERE object_id=OBJECT_ID(@tableName) AND ds.type != 'FG' ))

    IF @partitionScheme IS NOT NULL
    BEGIN
        SET @cmd += @newLine + 'ON '+@partitionScheme +'(PartitionId)'
    END

    EXEC (@cmd)

    DECLARE @indexName NVARCHAR(256)
    DECLARE IndexCursor CURSOR LOCAL FAST_FORWARD FOR
    SELECT name FROM sys.indexes WHERE object_id=OBJECT_ID(@tableName) ORDER BY index_id
    OPEN IndexCursor
    FETCH NEXT FROM IndexCursor INTO @indexName
    WHILE (@@FETCH_STATUS = 0)
    BEGIN
        EXEC AnalyticsInternal.prc_CloneOrReplaceIndex @indexName, @tableName, @partitionScheme, @newTableName
        FETCH NEXT FROM IndexCursor INTO @indexName
    END
    CLOSE IndexCursor
    DEALLOCATE IndexCursor

    RETURN 0
END

GO

