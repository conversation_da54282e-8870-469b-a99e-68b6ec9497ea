/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 16A3EDF9C5D873B42733F8FA6879AFAEBC9258DF
--------------------------------------------------------------------
-- <PERSON>des rework for a failed process batch
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_CreateProcessingRework
    @partitionId                        INT,
    @sourceBatchId                      BIGINT,
    @triggerTableName                   VARCHAR(64),
    @fromExistingState                  BIT,
    @createDependentWork                BIT,
    @delayReworkPerAttemptHistory       BIT,
    @ignoreWhenConsecutiveSprocFailures BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(2048)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    -- this is here to ensure defintion changes are reflected in the TransformState table
    -- could be done as host level servicing to avoid this call
    -- but the definitionsOnly option should be quick
    EXEC AnalyticsInternal.prc_iEnsureTransformState @partitionId, @definitionsOnly = 1

    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @sprocRecentFailureCount INT = 0
    DECLARE @attemptCount INT
    DECLARE @previousCreateDateTime DATETIME
    DECLARE @operationSproc VARCHAR(100)

    SELECT
        @attemptCount = ISNULL(b.ReworkAttemptCount, 0) + 1,
        @previousCreateDateTime = b.CreateDateTime,
        @operationSproc = b.OperationSproc
    FROM AnalyticsInternal.tbl_Batch b WITH (READPAST)
    WHERE PartitionId = @partitionId
        AND BatchId = @sourceBatchId
        AND OperationActive = 0
        AND OperationTriggerTableName = ISNULL(@triggerTableName, OperationTriggerTableName) -- sanity check, if passed
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@@ROWCOUNT = 0)
    BEGIN
        SET @errorMessage = 'Unknown Batch'
        SET @status = 1670001
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @errorMessage)
    END

    IF (@ignoreWhenConsecutiveSprocFailures = 1)
    BEGIN
        -- of the last 10 batches for this sproc, how many fails
        SELECT @sprocRecentFailureCount = COUNT(*)
        FROM
        (
            SELECT TOP 10 Failed
            FROM AnalyticsInternal.tbl_Batch
            WHERE PartitionId = @partitionId
            AND OperationSproc = @operationSproc
            ORDER BY BatchId DESC
        ) MostRecentBatches
        WHERE Failed = 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    IF (@sprocRecentFailureCount < 100)
    BEGIN
        DECLARE @scheduledDateTime DATETIME = NULL

        IF (@delayReworkPerAttemptHistory = 1)
        BEGIN
            -- re-attempt schedule is 1h, 4h, 16h, 64h, ...
            SET @scheduledDateTime = DATEADD(hour, POWER(2, (@attemptCount - 1) * 2), @previousCreateDateTime)
        END

        INSERT AnalyticsInternal.tbl_TransformRework
        (
            PartitionId,
            TriggerTableName,
            TriggerOperation,
            TargetTableName,
            TargetOperation,
            SProcName,
            SprocVersion,
            OperationScope,
            TransformOrder,
            TriggerBatchIdStart,
            TriggerBatchIdEnd,
            State,
            StateData,
            CreateDateTime,
            ScheduledDateTime,
            ReworkAttemptCount
        )
        SELECT
            b.PartitionId,
            tdef.TriggerTableName,
            tdef.TriggerOperation,
            tdef.TargetTableName,
            tdef.TargetOperation,
            tdef.SProcName,
            tdef.SprocVersion,
            tdef.OperationScope,
            tdef.TransformOrder,
            b.OperationTriggerBatchIdStart,
            b.OperationTriggerBatchIdEnd,
            IIF(@fromExistingState = 1 AND b.Ready = 0 AND b.OperationSproc = tdef.SprocName, b.OperationState, NULL),
            IIF(@fromExistingState = 1 AND b.Ready = 0 AND b.OperationSproc = tdef.SprocName, b.OperationStateData, NULL),
            @now,
            @scheduledDateTime,
            ISNULL(b.ReworkAttemptCount, 0) + 1
        FROM AnalyticsInternal.tbl_Batch b WITH (READPAST)
        JOIN AnalyticsInternal.tbl_TransformDefinition bdef
            ON b.OperationTriggerTableName = bdef.TriggerTableName
            AND b.TableName = bdef.TargetTableName
            AND b.Operation = bdef.TargetOperation
            AND b.OperationSproc = bdef.SProcName
        JOIN AnalyticsInternal.tbl_TransformDefinition tdef
            ON tdef.TriggerTableName = bdef.TriggerTableName
            AND (tdef.TargetTableName = bdef.TargetTableName OR @createDependentWork = 1)
            AND (tdef.TargetOperation = bdef.TargetOperation OR @createDependentWork = 1)
            AND (tdef.SProcName = bdef.SProcName OR @createDependentWork = 1)
            AND (
                -- processes inserts
                (tdef.TriggerOperation IN ('merge', 'insert', 'replace') AND bdef.TriggerOperation IN ('merge', 'insert', 'replace'))
                OR
                -- processes updates
                (tdef.TriggerOperation IN ('merge', 'update', 'replace') AND bdef.TriggerOperation IN ('merge', 'update', 'replace'))
                OR
                -- processes deletes
                (tdef.TriggerOperation IN ('delete', 'replace') AND bdef.TriggerOperation IN ('delete', 'replace'))
                OR
                -- processes timed
                (tdef.TriggerOperation IN ('time') AND bdef.TriggerOperation IN ('time'))
                )
            AND tdef.TransformOrder >= bdef.TransformOrder
        WHERE PartitionId = @partitionId
            AND BatchId = @sourceBatchId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        IF (@@ROWCOUNT = 0)
        BEGIN
            SET @errorMessage = 'Batch no longer valid'
            SET @status = 1670001
            SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @errorMessage)
        END

        -- dedupe
        -- if dupe is full reprocess (TriggerBatchIdStart <= 1) then choose the batch with the largest end batch, otherwise choose the newest
        -- important to choose newest to ensure any dependent rework is reschedueld to happen after base rework
        ;WITH Rework AS
        (
            SELECT ROW_NUMBER() OVER (PARTITION BY PartitionId, TriggerTableName, TriggerOperation, TriggerBatchIdStart, IIF(TriggerBatchIdStart > 1, TriggerBatchIdEnd, -1), TargetTableName, TargetOperation ORDER BY TriggerBatchIdEnd DESC, CreateDateTime DESC) row_num
            FROM AnalyticsInternal.tbl_TransformRework d
            WHERE PartitionId = @partitionId
        )
        DELETE r
        FROM Rework r
        WHERE row_num > 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

     END
END

GO

