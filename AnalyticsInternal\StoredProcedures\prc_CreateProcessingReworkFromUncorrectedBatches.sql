/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: C8C28569C7154999DFF3D7C5EE4BC546743E7F27
--------------------------------------------------------------------
-- Scheduledes rework for multiple failed process batches
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_CreateProcessingReworkFromUncorrectedBatches
    @partitionId                        INT NULL, -- optional
    @operationSproc                     VARCHAR(100) NULL, -- optional
    @fromExistingState                  BIT,
    @createDependentWork                BIT,
    @delayReworkPerAttemptHistory       BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(2048)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @pid INT
    DECLARE @bid BIGINT

    -- The name must not be #FailedBatch - we use that temp table in the prc_ValidateFailedBatches sproc
    CREATE TABLE #failedBatchTemp
    (
        PartitionId INT NOT NULL,
        BatchId     BIGINT NOT NULL
    )

    -- first pass - ensure failed batches are not already corrected
    INSERT  #failedBatchTemp(PartitionId, BatchId)
    SELECT  b.PartitionId,
            b.BatchId
    FROM    AnalyticsInternal.tbl_Batch b WITH (READPAST)
    JOIN    AnalyticsInternal.tbl_TransformDefinition bdef -- join to defs to make sure batch is still relevent
    ON      b.OperationTriggerTableName = bdef.TriggerTableName
            AND b.TableName = bdef.TargetTableName
            AND b.Operation = bdef.TargetOperation
            AND b.OperationSproc = bdef.SProcName
    WHERE   b.PartitionId = ISNULL(@partitionId, PartitionId)
            AND b.OperationSproc = ISNULL(@operationSproc, OperationSproc)
            AND b.OperationTriggerTableName IS NOT NULL
            AND b.Failed = 1
            AND b.ReworkBatchId IS NULL

    WHILE (EXISTS (SELECT * FROM #failedBatchTemp))
    BEGIN
        SELECT  TOP 1 @pid = PartitionId,
                @bid = IIF(COUNT(*) = 1, MAX(BatchId), NULL)
        FROM    #failedBatchTemp
        GROUP   BY PartitionId

        EXEC AnalyticsInternal.prc_ValidateFailedBatches    @pid,
                                                            @batchId = @bid

        DELETE  #failedBatchTemp
        WHERE   PartitionId = @pid
    END

    -- second pass - now gather batches again, and issue rework
    INSERT  #failedBatchTemp(PartitionId, BatchId)
    SELECT  b.PartitionId,
            b.BatchId
    FROM    AnalyticsInternal.tbl_Batch b WITH (READPAST)
    JOIN    AnalyticsInternal.tbl_TransformDefinition bdef -- join to defs to make sure batch is still relevent
    ON      b.OperationTriggerTableName = bdef.TriggerTableName
            AND b.TableName = bdef.TargetTableName
            AND b.Operation = bdef.TargetOperation
            AND b.OperationSproc = bdef.SProcName
    WHERE   b.PartitionId = ISNULL(@partitionId, PartitionId)
            AND b.OperationSproc = ISNULL(@operationSproc, OperationSproc)
            AND b.OperationTriggerTableName IS NOT NULL
            AND b.Failed = 1
            AND b.ReworkBatchId IS NULL

    WHILE (EXISTS (SELECT * FROM #failedBatchTemp))
    BEGIN
        SELECT TOP 1 @pid = PartitionId,
                @bid = BatchId
        FROM    #failedBatchTemp

        EXEC AnalyticsInternal.prc_CreateProcessingRework   @pid,
                                                            @sourceBatchId = @bid,
                                                            @triggerTableName = NULL,
                                                            @fromExistingState = @fromExistingState,
                                                            @createDependentWork = @createDependentWork,
                                                            @delayReworkPerAttemptHistory = @delayReworkPerAttemptHistory,
                                                            @ignoreWhenConsecutiveSprocFailures = 0

        DELETE  #failedBatchTemp
        WHERE   PartitionId = @pid
                AND BatchId = @bid
    END

    DROP TABLE #failedBatchTemp
END

GO

