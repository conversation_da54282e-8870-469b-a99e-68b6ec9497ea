/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 91059546B502E058EC4EC5B31BAA55C81FF85D0A
CREATE PROCEDURE AnalyticsInternal.prc_CreateStream
    @partitionId INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @enabled BIT,
    @priority INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @data AnalyticsInternal.typ_CreateStreamsData

    INSERT  @data
    SELECT  @tableName,
            @providerShardId,
            @priority

    EXECUTE AnalyticsInternal.prc_iCreateStreams
            @partitionId,
            @data,
            @enabled,
            false, -- keysOnly
            @now
END

GO

