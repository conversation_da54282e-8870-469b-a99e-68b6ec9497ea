/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F17DF21C0653B568E310F16758D7B0FFA12748A6
--=================================
--Check if a process has too many custom fields of a certain type and overflowing custom tables
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_CustomWorkItemRevision_TooManyFields
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN

    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @expectedCount BIGINT = 0;
    DECLARE @actualCount BIGINT
    DECLARE @failed BIT
    DECLARE @kpiValue FLOAT;

    WITH maxSupportedColumnName (ModelColumnName) AS
    (
        SELECT      MAX(c.name)
        FROM        sys.columns c
        JOIN        sys.tables t WITH (READPAST)
        ON          t.object_id = c.object_id
        JOIN        sys.schemas s WITH (READPAST)
        ON          s.schema_id = t.schema_id
        WHERE       t.name like 'tbl_WorkItemRevisionCustom%'
                    AND s.name = 'AnalyticsModel'
        GROUP BY    c.system_type_id
    ),
    maxFoundColumnName (FieldType, ModelColumnName) AS
    (
        SELECT      FieldType, MAX(ModelColumnName)
        FROM        AnalyticsInternal.tbl_ProcessField p
        WHERE       PartitionId = @partitionId
        GROUP BY    FieldType
    )
    SELECT  @actualCount = COUNT(*)
    FROM    maxSupportedColumnName s
    JOIN    maxFoundColumnName f ON f.ModelColumnName = s.ModelColumnName
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), HASH GROUP);

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = @actualCount --# of fieldtypes that reached max

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @now,
        @compareStartDate,
        @compareEndDate,
        @name,
        NULL, -- scope
        'Model.WorkItem',
        @expectedCount,
        @actualCount,
        @kpiValue,
        @failed

    RETURN 0
END

GO

