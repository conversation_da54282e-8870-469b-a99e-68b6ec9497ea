/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B7149B6094B314977E7C34AA658633A6F9A95785
CREATE PROCEDURE AnalyticsInternal.prc_DQ_IsLastRevisionOfDay
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
-- This DQ along with prc_DQ_RevisedDateAndChangedDateGapCheck covers all scenario's correctly.
-- If prc_DQ_RevisedDateAndChangedDateGapCheck is not returning correct result, then this might give wrong output.
BEGIN
    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @compareStartDate DATETIME = GETUTCDATE()
    DECLARE @compareEndDate DATETIME = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT = 0

    SELECT @kpiValue = SUM(num),
           @failed = IIF(COUNT(*) > 0 ,1 , 0)
    FROM (
        SELECT   1 AS num
        FROM     AnalyticsModel.vw_WorkItemRevision WITH (NOLOCK)
        WHERE    PartitionId = @partitionId
        AND      IsLastRevisionOfDay = 1
        AND      AnalyticsUpdatedDate < @compareEndDate
        GROUP BY WorkItemId, ChangedDateSK
        HAVING   COUNT(*) <> 1
    ) T
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
            @partitionId,
            @now,
            @compareStartDate,
            @compareEndDate,
            @name,
            NULL, -- scope
            'Model.WorkItem',
            0,
            @kpiValue,
            @kpiValue,
            @failed

    RETURN 0
END

GO

