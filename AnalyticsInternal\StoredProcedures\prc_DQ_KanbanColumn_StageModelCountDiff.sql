/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E720943E076FFC6228CB913E8CBDF9C72938548F
--=================================
--Compare the distinct row counts between Stage Kanban Columns table and Model Board Location table.
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_KanbanColumn_StageModelCountDiff
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @expectedCount BIGINT, @actualCount BIGINT;
    DECLARE @failed BIT;
    DECLARE @kpiValue FLOAT;

   --Retrieve distinct model board locations
    WITH DistinctBoardLocations AS (
        SELECT DISTINCT
            ColumnId,
            ColumnOrder,
            ColumnName,
            ColumnItemLimit,
            IsColumnSplit
        FROM [AnalyticsModel].[tbl_BoardLocation]
        WHERE PartitionId = @partitionId
        AND BoardId IS NOT NULL -- null boards can come from TeamSetting.BacklogCategories
    ),
    --Retrieve distinct kanban board columns
    DistinctKanbanBoardColumns AS (
        SELECT DISTINCT
            Id,
            [Order],
            Name,
            ItemLimit,
            IsSplit
        FROM [AnalyticsStage].[tbl_KanbanBoardColumn] WHERE PartitionId = @partitionId
    )
    --Record exprected and actual row counts for [AnalyticsModel].[tbl_BoardLocation]
    SELECT
        @expectedCount = (SELECT COUNT(*) FROM DistinctKanbanBoardColumns),
        @actualCount = (SELECT COUNT(*) FROM DistinctBoardLocations)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = IIF(@expectedCount = @actualCount, 0, IIF(@expectedCount = 0, 1.0, (@actualCount - @expectedCount) * 1.0 / @expectedCount))

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @now,
        @compareStartDate,
        @now, --Not using compareEndDate intentionally to compare data across all time
        @name,
        NULL, -- scope
        'Model.BoardLocation',
        @expectedCount,
        @actualCount,
        @failed,
        @kpiValue

    RETURN 0

END

GO

