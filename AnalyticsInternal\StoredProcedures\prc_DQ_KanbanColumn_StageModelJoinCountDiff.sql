/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FEC54CB2A3D21E1041A54159BEBB3AEF71DF5A71
--=================================
--Check that the rows added and updated in Stage Kanban Columns table have been transformed to the Model Board Location table.
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_KanbanColumn_StageModelJoinCountDiff
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @expectedCount BIGINT, @actualCount BIGINT;
    DECLARE @failed BIT;
    DECLARE @kpiValue FLOAT;

    --Filter out the stage rows that have been updated recently. They may not have been transformed yet due to latency.
    WITH FilteredSource AS (
         SELECT DISTINCT
             Id,
             [Order],
             Name,
             ItemLimit,
             IsSplit
         FROM [AnalyticsStage].[tbl_KanbanBoardColumn]
         WHERE PartitionId = @partitionId AND AnalyticsUpdatedDate < @compareEndDate
    ),
    --Check for differences between the columns.
    Diff AS(
        SELECT * FROM FilteredSource
        INTERSECT
        SELECT DISTINCT
            ColumnId,
            ColumnOrder,
            ColumnName,
            ColumnItemLimit,
            IsColumnSplit
        FROM [AnalyticsModel].[tbl_BoardLocation]
        WHERE PartitionId = @partitionId
        AND BoardId IS NOT NULL -- null boards can come from TeamSetting.BacklogCategories
    )
    --Record exprected and actual row counts for [AnalyticsModel].[tbl_BoardLocation]
    SELECT
        @expectedCount = (SELECT COUNT(*) FROM FilteredSource),
        @actualCount = (SELECT COUNT(*) FROM Diff)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = IIF(@expectedCount = @actualCount, 0, IIF(@expectedCount = 0, 1.0, (@actualCount - @expectedCount) * 1.0 / @expectedCount))

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @now,
        @compareStartDate,
        @now,
        @name,
        NULL, -- scope
        'Model.BoardLocation',
        @expectedCount,
        @actualCount,
        @kpiValue,
        @failed

    RETURN 0
END

GO

