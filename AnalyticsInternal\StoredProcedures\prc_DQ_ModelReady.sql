/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 1EBAC2FE889AED3941A2E3D8037DCE5466A43F62
CREATE PROCEDURE AnalyticsInternal.prc_DQ_ModelReady
    @partitionId INT,
    @name VA<PERSON>HA<PERSON>(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME2
AS
BEGIN
    DECLARE @tableName VARCHAR(64)
    DECLARE @results AnalyticsInternal.typ_DataQualityResult3

    DECLARE @runDate DATETIME
    DECLARE @startDate DATETIME
    DECLARE @endDate DATETIME
    DECLARE @expectedValue BIGINT
    DECLARE @actualValue BIGINT
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT

    CREATE TABLE #Tables
    (
        TargetTable     VARCHAR(64)     NOT NULL
    )

    -- Only re-run for failed tests (seeded by prc_InitializeModelReady on fault on)
    ;WITH t AS (
        SELECT  TargetTable,
                Failed,
                ROW_NUMBER() OVER (PARTITION BY TargetTable ORDER BY RunDate DESC) AS rn
        FROM    AnalyticsInternal.tbl_DataQualityResult
        WHERE   PartitionId = @partitionId
                AND Name = @name
    )
    INSERT INTO #Tables
    SELECT DISTINCT TargetTable
    FROM    t
    WHERE   t.rn = 1
            AND Failed = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE TableCursor CURSOR FAST_FORWARD FOR
    SELECT  TargetTable
    FROM    #Tables

    OPEN TableCursor

    FETCH NEXT FROM TableCursor
    INTO @tableName

    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC AnalyticsInternal.prc_iGenericModelReadyTest
            @partitionId,
            @name,
            @tableName,
            @runDate OUTPUT,
            @startDate OUTPUT,
            @endDate OUTPUT,
            @expectedValue OUTPUT,
            @actualValue OUTPUT,
            @kpiValue OUTPUT,
            @failed OUTPUT

        INSERT INTO @results (
            Scope,
            TargetTable,
            RunDate,
            StartDate,
            EndDate,
            ExpectedValue,
            ActualValue,
            KpiValue,
            Failed)
        VALUES (
            @tableName,
            @tableName,
            @runDate,
            @startDate,
            @endDate,
            @expectedValue,
            @actualValue,
            @kpiValue,
            @failed)

        FETCH NEXT FROM TableCursor
        INTO @tableName
    END

    EXEC AnalyticsInternal.prc_iRecordDataQuality
        @partitionId,
        @name,
        @results

    CLOSE TableCursor
    DEALLOCATE TableCursor
    DROP TABLE #Tables

    RETURN 0
END

GO

