/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 4398F63D6283F77DF2876847819D869C0A838104
--=================================
--Check all cold start staged data are available in model
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_Project_ModelReady
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();

    --All shards must be loaded but within a shard, at least one stream has to be loaded
    IF NOT EXISTS
    (
        SELECT      TOP 1 *
        FROM        AnalyticsInternal.tbl_TableProviderShardStream
        WHERE       PartitionId = @partitionId
                    AND TableName = 'Project'
    )
    OR EXISTS
    (
        SELECT      AnalyticsProviderShardId, MAX(LoadedTime)
        FROM        AnalyticsInternal.tbl_TableProviderShardStream
        WHERE       PartitionId = @partitionId
                    AND TableName = 'Project'
        GROUP BY    AnalyticsProviderShardId
        HAVING      MAX(LoadedTime) IS NULL
    )
    BEGIN
        EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
                @partitionId,
                @now, --@runDate
                @now, --@startDate
                @now, --@endDate
                @name,
                'Model.Project', --@scope
                'Model.Project', --@targetTable
                0, --@expectedValue
                0, --@actualValue
                -99999999, --@kpiValue
                1 --@failed
    END
    ELSE
    BEGIN

        DECLARE @maxLoadedTime DATETIME
        SELECT  @maxLoadedTime = MAX(LoadedTime)
        FROM    AnalyticsInternal.tbl_TableProviderShardStream
        WHERE   PartitionId = @partitionId
                AND TableName = 'Project'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        DECLARE @exclusionTime DATETIME
        SELECT  @exclusionTime = DATEADD(SECOND, -1 * @latencyExclusionSeconds, @now)

        DECLARE @compareStartDate DATETIME = '1900-01-01'
        DECLARE @compareEndDate DATETIME = IIF(@maxLoadedTime > @exclusionTime, @maxLoadedTime, @exclusionTime)
        DECLARE @expectedCount BIGINT, @actualCount BIGINT
        DECLARE @kpiValue FLOAT
        DECLARE @failed BIT
        DECLARE @result AnalyticsInternal.typ_DataQualityResult3

        EXEC AnalyticsInternal.prc_Project_StageModelCountDiff
            @partitionId,
            @compareStartDate,
            @compareEndDate,
            @expectedCount OUTPUT,
            @actualCount OUTPUT,
            @kpiValue OUTPUT,
            @failed OUTPUT

        SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
        SELECT @kpiValue = @expectedCount - @actualCount

        EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
            @partitionId,
            @now,
            @compareStartDate,
            @compareEndDate,
            @name,
            'Model.Project',
            'Model.Project',
            @expectedCount,
            @actualCount,
            @kpiValue,
            @failed

    END

    RETURN 0
END

GO

