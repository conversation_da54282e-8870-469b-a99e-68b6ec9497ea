/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 6916004822754B4F2CBDCE15A2693F9931C2B8C6
--=================================
--Check that the rows added and updated in Stage Work Item Revisions table have been transformed to the Model Work Item Revisions table.
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_Project_StageModelCountDiff
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @expectedCount BIGINT, @actualCount BIGINT
    DECLARE @failed BIT
    DECLARE @kpiValue FLOAT

    EXEC AnalyticsInternal.prc_Project_StageModelCountDiff
        @partitionId,
        @compareStartDate,
        @compareEndDate,
        @expectedCount OUTPUT,
        @actualCount OUTPUT,
        @kpiValue OUTPUT,
        @failed OUTPUT

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @now,
        @compareStartDate,
        @compareEndDate,
        @name,
        'Model.Project',
        'Model.Project',
        @expectedCount,
        @actualCount,
        @kpiValue,
        @failed

    RETURN 0
END

GO

