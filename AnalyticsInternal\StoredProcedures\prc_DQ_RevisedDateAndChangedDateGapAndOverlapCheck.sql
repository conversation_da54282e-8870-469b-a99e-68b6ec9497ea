/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AF5FE8477418726574D9F3621723193B13B5CE61
CREATE PROCEDURE AnalyticsInternal.prc_DQ_RevisedDateAndChangedDateGapAnd<PERSON><PERSON><PERSON><PERSON>he<PERSON>
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = @now
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT = 0

    ;
    WITH WorkItems AS
    (
        SELECT  r1.PartitionId,
                r1.WorkItemId,
                r1.ChangedDate,
                r1.RevisedDate
        FROM    AnalyticsModel.tbl_WorkItemHistory AS r1 WITH(INDEX(CL_AnalyticsModel_tbl_WorkItemHistory),NOLOCK)
        WHERE   r1.PartitionId = @partitionId

        UNION ALL

        SELECT  r2.PartitionId,
                r2.WorkItemId,
                r2.ChangedDate,
                '9999-01-01' AS RevisedDate
        FROM    AnalyticsModel.tbl_WorkItem AS r2 WITH(INDEX(CL_AnalyticsModel_tbl_WorkItem),NOLOCK)
        WHERE   r2.PartitionId = @partitionId
    )
    SELECT  @kpiValue = SUM(num),
            @failed = IIF(COUNT(*) > 0, 1, 0)
    FROM (
        SELECT   1 as num
        FROM     WorkItems
        GROUP BY WorkItemId
        HAVING   DATEDIFF_BIG(SECOND, MIN(ChangedDate), @now) - SUM(DATEDIFF_BIG(SECOND, ChangedDate, IIF('9999-01-01' = RevisedDate, @now,RevisedDate))) <> 0
    ) T
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
            @partitionId,
            @now,
            @compareStartDate,
            @compareEndDate,
            @name,
            NULL, -- scope
            'Model.WorkItemHistory',
            0,
            @kpiValue,
            @kpiValue,
            @failed
    RETURN 0
END

GO

