/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 61A94781CCAB000C7C0BD0C27699A7CC881F02A1
CREATE PROCEDURE AnalyticsInternal.prc_DQ_TableHolds
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @result AnalyticsInternal.typ_DataQualityResult3

    ;WITH TransformHoldTime AS
    (
        SELECT  TargetTableName,
                MAX(IIF(Hold = 1, DATEDIFF(SECOND, HoldChangedTime, @now), NULL)) AS HoldSeconds
        FROM    AnalyticsInternal.tbl_TransformState s
        WHERE   PartitionId = @partitionId
        GROUP BY TargetTableName
    )
    , StageHoldTime AS
    (
        SELECT  TableName AS TargetTableName,
                MAX(IIF(Maintenance = 1, DATEDIFF(SECOND, MaintenanceChangedTime, @now), NULL)) AS HoldSeconds
        FROM    AnalyticsInternal.tbl_TableProviderShardStream s
        WHERE   PartitionId = @partitionId
        GROUP BY TableName
    )
    , HoldTime AS
    (
        SELECT  TargetTableName,
                MAX(HoldSeconds) AS HoldSeconds
        FROM
        (
            SELECT  TargetTableName,
                    HoldSeconds
            FROM    TransformHoldTime
            UNION ALL
            SELECT  TargetTableName,
                    HoldSeconds
            FROM    StageHoldTime
        ) allHoldTime
        GROUP BY TargetTableName
    )
    INSERT  @result (Scope, TargetTable, RunDate, StartDate, EndDate, ExpectedValue, ActualValue, KpiValue, Failed)
    SELECT  TargetTableName,
            TargetTableName,
            @now,
            '1900-01-01' AS StartDate,
            @now AS EndDate,
            0 AS ExpectedValue,
            ISNULL(HoldSeconds, 0) AS ActualValue,
            ISNULL(HoldSeconds, 0) AS KpiValue,
            IIF(HoldSeconds >= @latencyExclusionSeconds, 1, 0) AS Failed
    FROM    HoldTime
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    EXEC AnalyticsInternal.prc_iRecordDataQuality
        @partitionId,
        @name,
        @result

    RETURN 0
END

GO

