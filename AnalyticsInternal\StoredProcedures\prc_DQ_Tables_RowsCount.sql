/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 36C562B002C369AEAAB45F4984314CB2E38089B1
CREATE PROCEDURE AnalyticsInternal.prc_DQ_Tables_RowsCount
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @compareStartDate DATETIME = '1900-01-01'
    DECLARE @compareEndDate DATETIME = @now
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT = 0
    DECLARE @result AnalyticsInternal.typ_DataQualityResult3

    -- Use AnalyticsModel.tbl_WorkItem and AnalyticsModel.tbl_WorkItemHistory instead of of
    -- AnalyticsStage.tbl_WorkItemRevision for better performance (x4 improvement).

    -- AnalyticsModel.tbl_WorkItem
    SELECT  @kpiValue = COUNT(*)
    FROM    AnalyticsModel.tbl_WorkItem WITH (NOLOCK)
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  @result (TargetTable, Scope, RunDate, StartDate, EndDate, KpiValue, Failed)
    SELECT  'Model.WorkItem',
            'Model.WorkItem',
            @now,
            @compareStartDate,
            @compareEndDate,
            @kpiValue,
            @failed

    -- AnalyticsModel.tbl_WorkItemHistory
    SELECT  @kpiValue = COUNT(*)
    FROM    AnalyticsModel.tbl_WorkItemHistory WITH (NOLOCK)
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  @result (TargetTable, Scope, RunDate, StartDate, EndDate, KpiValue, Failed)
    SELECT  'Model.WorkItemHistory',
            'Model.WorkItemHistory',
            @now,
            @compareStartDate,
            @compareEndDate,
            @kpiValue,
            @failed

    EXEC    AnalyticsInternal.prc_iRecordDataQuality
            @partitionId,
            @name,
            @result

    RETURN 0
END

GO

