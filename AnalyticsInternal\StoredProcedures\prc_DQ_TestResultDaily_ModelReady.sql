/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 77107EDD501A9539810179659D541D40FF014DC6
CREATE PROCEDURE AnalyticsInternal.prc_DQ_TestResultDaily_ModelReady
    @partitionId INT,
    @name VA<PERSON>HA<PERSON>(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME2
AS
BEGIN
    DECLARE @targetTable VARCHAR(256) = 'Model.TestResultDaily'
    DECLARE @runDate DATETIME
    DECLARE @startDate DATETIME
    DECLARE @endDate DATETIME
    DECLARE @expectedValue BIGINT
    DECLARE @actualValue BIGINT
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT

    EXEC AnalyticsInternal.prc_iGenericModelReadyTest
        @partitionId,
        @name,
        @targetTable,
        @runDate OUTPUT,
        @startDate OUTPUT,
        @endDate OUTPUT,
        @expectedValue OUTPUT,
        @actualValue OUTPUT,
        @kpiValue OUTPUT,
        @failed OUTPUT

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @runDate,
        @startDate,
        @endDate,
        @name,
        @targetTable, --@scope
        @targetTable, --@targetTable
        @expectedValue,
        @actualValue,
        @kpiValue,
        @failed

    RETURN 0
END

GO

