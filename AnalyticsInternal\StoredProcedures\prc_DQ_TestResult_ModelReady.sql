/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 770E6286CB7419C84C68B22329AF8829BDDD8409
CREATE PROCEDURE AnalyticsInternal.prc_DQ_TestResult_ModelReady
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME2
AS
BEGIN
    DECLARE @targetTable VARCHAR(256) = 'Model.TestResult'
    DECLARE @runDate DATETIME
    DECLARE @startDate DATETIME
    DECLARE @endDate DATETIME
    DECLARE @expectedValue BIGINT
    DECLARE @actualValue BIGINT
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT

    EXEC AnalyticsInternal.prc_iGenericModelReadyTest
        @partitionId,
        @name,
        @targetTable,
        @runDate OUTPUT,
        @startDate OUTPUT,
        @endDate OUTPUT,
        @expectedValue OUTPUT,
        @actualValue OUTPUT,
        @kpiValue OUTPUT,
        @failed OUTPUT

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @runDate,
        @startDate,
        @endDate,
        @name,
        @targetTable, --@scope
        @targetTable, --@targetTable
        @expectedValue,
        @actualValue,
        @kpiValue,
        @failed

    RETURN 0
END

GO

