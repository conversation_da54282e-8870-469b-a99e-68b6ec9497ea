/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F6DF955DC300FB05DED626C7E0411642FF4DAE85
CREATE PROCEDURE AnalyticsInternal.prc_DQ_TestRun_ModelReady
    @partitionId INT,
    @name VA<PERSON>HA<PERSON>(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME2
AS
BEGIN
    DECLARE @targetTable VARCHAR(256) = 'Model.TestRun'
    DECLARE @runDate DATETIME
    DECLARE @startDate DATETIME
    DECLARE @endDate DATETIME
    DECLARE @expectedValue BIGINT
    DECLARE @actualValue BIGINT
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT

    EXEC AnalyticsInternal.prc_iGenericModelReadyTest
        @partitionId,
        @name,
        @targetTable,
        @runDate OUTPUT,
        @startDate OUTPUT,
        @endDate OUTPUT,
        @expectedValue OUTPUT,
        @actualValue OUTPUT,
        @kpiValue OUTPUT,
        @failed OUTPUT

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @runDate,
        @startDate,
        @endDate,
        @name,
        @targetTable, --@scope
        @targetTable, --@targetTable
        @expectedValue,
        @actualValue,
        @kpiValue,
        @failed

    RETURN 0
END

GO

