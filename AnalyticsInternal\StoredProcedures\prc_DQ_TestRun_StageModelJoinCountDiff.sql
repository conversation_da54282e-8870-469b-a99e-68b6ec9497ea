/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AE092DE71F08DF492894B2EA2CD35E72E139DB4D
--=================================
--Check that the rows added and updated in Stage Work Item Revisions table have been transformed to the Model Work Item Revisions table.
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_TestRun_StageModelJoinCountDiff
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME2 = SYSUTCDATETIME();
    DECLARE @compareStartDate DATETIME2 = '1900-01-01'
    DECLARE @compareEndDate DATETIME2 = DATEADD(second, 0 - @latencyExclusionSeconds, @now)
    DECLARE @expectedCount BIGINT, @actualCount BIGINT
    DECLARE @failed BIT
    DECLARE @kpiValue FLOAT
    DECLARE @status INT

    --Get the retention date
    DECLARE @registryQueryResult    NVARCHAR(MAX),
            @retentionDays          INT = 180,
            @key                    NVARCHAR(256) = REPLACE(N'#' + '/Service/Analytics/Settings/Transform/' + 'Model.TestRun' + '/' + 'RetentionDays' + '/', '/', '\')

    EXEC @status = prc_pQueryRegistry @partitionId = @partitionId,
                                    @key = @key,
                                    @value = @registryQueryResult OUTPUT
    IF (@status = 0 AND @registryQueryResult IS NOT NULL)
    BEGIN
        SET @retentionDays = CAST(@registryQueryResult AS INT)
    END

    SET @compareStartDate = DATEADD(day, 0 - @retentionDays, @now)

    -- expected is number of rows in stage, within the retention period
    -- actual is number of matching rows in model
    SELECT  @expectedCount = COUNT(*), @actualCount = SUM(IIF(m.PartitionId IS NOT NULL, 1, 0))
    FROM    AnalyticsStage.tbl_TestRun s WITH (NOLOCK)
    LEFT HASH JOIN AnalyticsModel.tbl_TestRun m
    ON      s.PartitionId = m.PartitionId
            AND s.TestRunId = m.TestRunId
    WHERE   s.PartitionId = @partitionId
            AND s.DateCompleted < @compareEndDate
            AND s.DateCompleted > @compareStartDate
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = @expectedCount - @actualCount

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @now,
        @compareStartDate,
        @compareEndDate,
        @name,
        'Model.TestRun',
        'Model.TestRun',
        @expectedCount,
        @actualCount,
        @kpiValue,
        @failed

    RETURN 0
END

GO

