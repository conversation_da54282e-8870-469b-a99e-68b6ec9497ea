/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 7E203CE0A74A01DA0D78E09388925D498395A5CE
CREATE PROCEDURE AnalyticsInternal.prc_DQ_Test_ModelReady
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME2
AS
BEGIN
    DECLARE @targetTable VARCHAR(256) = 'Model.Test'
    DECLARE @runDate DATETIME
    DECLARE @startDate DATETIME
    DECLARE @endDate DATETIME
    DECLARE @expectedValue BIGINT
    DECLARE @actualValue BIGINT
    DECLARE @kpiValue FLOAT
    DECLARE @failed BIT

    EXEC AnalyticsInternal.prc_iGenericModelReadyTest
        @partitionId,
        @name,
        @targetTable,
        @runDate OUTPUT,
        @startDate OUTPUT,
        @endDate OUTPUT,
        @expectedValue OUTPUT,
        @actualValue OUTPUT,
        @kpiValue OUTPUT,
        @failed OUTPUT

    EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
        @partitionId,
        @runDate,
        @startDate,
        @endDate,
        @name,
        @targetTable, --@scope
        @targetTable, --@targetTable
        @expectedValue,
        @actualValue,
        @kpiValue,
        @failed

    RETURN 0
END

GO

