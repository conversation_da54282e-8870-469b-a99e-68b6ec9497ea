/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AD4E59369D4338A45F436F6DA516C98AC8126D68
CREATE PROCEDURE AnalyticsInternal.prc_DQ_WorkItemRevision_ModelLatency
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    -- special case for test - negative latency exclusion allow for testing bins including now (future end dates)
    -- otherwise ignore exclusion
    DECLARE @maxBinEndDate DATETIME = IIF(@latencyExclusionSeconds < 0, DATEADD(second, 0 - @latencyExclusionSeconds, @now), @now)
    DECLARE @result AnalyticsInternal.typ_DataQualityResult3

    SET @previousEndDate = ISNULL(@previousEndDate, '1900-01-01') -- ensure a non-null value

    ;WITH RowLatency AS
    (
        SELECT  GETUTCDATE() AS RunDate,
                DATEADD(minute, DATEDIFF_BIG(minute, 0, AnalyticsCreatedDate), 0) AS BinStartDate,
                DATEADD(minute, DATEDIFF_BIG(minute, 0, AnalyticsCreatedDate) + 1, 0) AS BinEndDate,
                DATEDIFF_BIG(second, AuthorizedDate, AnalyticsCreatedDate) AS Latency
        FROM    AnalyticsModel.tbl_WorkItem r WITH (READPAST)
        WHERE   AnalyticsCreatedDate >= @previousEndDate -- previousEndDate is exclusive for previous bin, so include it here
                AND r.PartitionId = @partitionId
    )
    INSERT  @result (TargetTable, RunDate, StartDate, EndDate, ActualValue, KpiValue, Failed)
    SELECT  'Model.WorkItem',
            @now,
            BinStartDate,
            BinEndDate,
            MAX(Latency) AS ActualValue,
            MAX(Latency) AS KpiValue,
            IIF(MAX(Latency) > 300, 1, 0) AS Failed
    FROM    RowLatency
    WHERE   BinEndDate < @maxBinEndDate -- don't include values from the current bin
    GROUP BY BinStartDate, BinEndDate
    ORDER BY BinStartDate, BinEndDate
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- if we have no data for previous 15 minutes, try filling in latency up to that point from stage table, in case transforms are not running
    SELECT @previousEndDate = ISNULL(MAX(EndDate), @previousEndDate) FROM @result
    SET @maxBinEndDate = DATEADD(second, -900, @maxBinEndDate)
    IF (@previousEndDate < @maxBinEndDate)
    BEGIN
        DECLARE @triggerBatchIdMin BIGINT
        DECLARE @triggerBatchIdMax BIGINT

        SELECT  @triggerBatchIdMin = ISNULL(MIN(DoneBatchIdEnd), 0) + 1,
                @triggerBatchIdMax = MAX(TriggerBatchIdMax)
        FROM    AnalyticsInternal.tbl_TransformState
        WHERE   PartitionId = @partitionId
                AND TargetTableName = 'Model.WorkItem'
                AND TriggerTableName = 'WorkItemRevision'
                AND TriggerOperation = 'insert'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        ;WITH StageRowLatency AS
        (
            SELECT  DATEADD(minute, DATEDIFF_BIG(minute, 0, AnalyticsCreatedDate), 0) AS BinStartDate,
                    DATEADD(minute, DATEDIFF_BIG(minute, 0, AnalyticsCreatedDate) + 1, 0) AS BinEndDate,
                    DATEDIFF_BIG(second, MIN(System_AuthorizedDate) OVER (PARTITION BY NULL), @now) AS Latency
            FROM    AnalyticsStage.tbl_WorkItemRevision WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdCreated))
            WHERE   PartitionId = @partitionId
                    AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdMin AND @triggerBatchIdMax
        )
        INSERT  @result (TargetTable, RunDate, StartDate, EndDate, ActualValue, KpiValue, Failed)
        SELECT  'Model.WorkItem',
                @now,
                BinStartDate,
                BinEndDate,
                MAX(Latency) AS ActualValue,
                MAX(Latency) AS KpiValue,
                IIF(MAX(Latency) > 300, 1, 0) AS Failed
        FROM    StageRowLatency
        WHERE   BinEndDate < @maxBinEndDate -- don't include values from the current bin
                AND BinStartDate >= @previousEndDate
        GROUP BY BinStartDate, BinEndDate
        ORDER BY BinStartDate, BinEndDate
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdMin = 0, @triggerBatchIdMax = 1))
    END

    EXEC AnalyticsInternal.prc_iRecordDataQuality
        @partitionId,
        @name,
        @result

    RETURN 0

END

GO

