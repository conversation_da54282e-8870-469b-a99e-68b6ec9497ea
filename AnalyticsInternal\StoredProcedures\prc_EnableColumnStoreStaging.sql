/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 4F569DB8D129F808D84154FDA68B7C66990D9220
CREATE PROCEDURE AnalyticsInternal.prc_EnableColumnStoreStaging
    @columnStoreIndexName NVARCHAR(128),
    @partitionNumber INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    SET DEADLOCK_PRIORITY LOW
    --Should support followings:
    --No CCI because of sql version OR edition => do nothing
    --CCI is enabled. The target table has 1 or more partitions: ALTER INDEX should provide PARTITION option
    --continue if column store index exists
    DECLARE @tableName NVARCHAR(128)
    DECLARE @isNCCI BIT
    SELECT  @tableName = OBJECT_SCHEMA_NAME(object_id) + '.' + OBJECT_NAME(object_id),
            @isNCCI = IIF ([type] = 6, 1, 0)
    FROM    sys.indexes
    WHERE   [type] IN (5, 6) --either CCI or nCCI
            AND name = @columnStoreIndexName
            AND (
                OBJECT_NAME(object_id) = 'tbl_WorkItemRevision' AND OBJECT_SCHEMA_NAME(object_id) = 'AnalyticsModel'
                OR OBJECT_NAME(object_id) LIKE 'tbl_WorkItemRevisionCustom[0-9][0-9]' AND OBJECT_SCHEMA_NAME(object_id) = 'AnalyticsStage'
            )
    -- For nCCI indexes reorganize we need to enable staging => get PartitionId range for that operation
    DECLARE @startPartitionId INT
    DECLARE @endPartitionId INT
    IF @isNCCI = 1
    BEGIN
        SET @endPartitionId = (
            SELECT  CAST(value AS INT)
            FROM    sys.partition_range_values rv
            JOIN    sys.partition_functions pf
            ON      rv.function_id=pf.function_id
            WHERE   rv.boundary_id = @partitionNumber
                    AND pf.name = 'func_AnalyticsWorkItemPartition')
        SET @startPartitionId = ISNULL((
            SELECT  CAST(value AS INT)
            FROM    sys.partition_range_values rv
            JOIN    sys.partition_functions pf
            ON      rv.function_id=pf.function_id
            WHERE   rv.boundary_id = (@partitionNumber  - 1)
                    AND pf.name = 'func_AnalyticsWorkItemPartition') +1, 0)
    END
    IF @tableName IS NOT NULL
    BEGIN
            IF @startPartitionId IS NOT NULL
            BEGIN
                EXEC AnalyticsInternal.prc_SetStagingTableMaintenance 'WorkItemRevision', 0, @startPartitionId, @endPartitionId, null
            END
    END
END

GO

