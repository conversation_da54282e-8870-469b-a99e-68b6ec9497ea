/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 652663B7985AB3B1F0AE2C0CB281E67548CC108B
--------------------------------------------------------------------
-- Marks a transform batch as failed
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_FailTransformBatch
    @partitionId    INT,
    @batchId        BIGINT,
    @settings       typ_KeyValuePairStringTable READONLY,
    @failedMessage  NVARCHAR(1000),
    @incDurationMS  INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @maxAttemptCount INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = CONCAT('/Service/Analytics/Settings/Transform/', 'TransformNextBatch/MaxAttempts')), 3)

    UPDATE AnalyticsInternal.tbl_Batch WITH (ROWLOCK)
    SET Failed = IIF(ISNULL(AttemptCount, @maxAttemptCount) >= IIF(ReworkAttemptCount > 0 AND OperationSubBatchCount = 1, 1, @maxAttemptCount), 1, 0),
        FailedCount = ISNULL(FailedCount, 0) + 1,
        OperationActive = 0,
        FailedMessage = ISNULL(@failedMessage, FailedMessage),
        OperationDurationMS = IIF(@incDurationMS IS NOT NULL, ISNULL(OperationDurationMS, 0) + @incDurationMS, OperationDurationMS),
        LastFailedOperationSubBatchCount = OperationSubBatchCount
    WHERE PartitionId = @partitionId
        AND BatchId = @batchId
        AND Ready = 0
        AND Failed = 0
        AND OperationActive = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SELECT
        @partitionId AS PartitionId,
        CAST(0 AS BIT) AS Complete,
        CAST(0 AS BIT) AS LowPriorityDeferred,
        BatchId AS BatchId,
        OperationSubBatchCount AS SubBatchCount,
        AttemptCount AS AttemptCount,
        DATEADD(millisecond, 0 - ISNULL(@incDurationMS, 0), GETUTCDATE()) AS StartDate,
        OperationSproc AS Sproc,
        OperationSprocVersion AS SprocVersion,
        OperationTriggerTableName AS TriggerTableName,
        TableName AS TableName,
        OperationTriggerBatchIdStart AS TriggerBatchIdStart,
        OperationTriggerBatchIdEnd AS TriggerBatchIdEnd,
        OperationState AS [State],
        OperationStateData AS StateData,
        OperationState AS EndState,
        OperationStateData AS EndStateData,
        CAST(0 AS BIT) AS Held,
        Ready AS Ready,
        Failed AS Failed,
        CAST(1 AS BIT) AS FailedAttempt,
        FailedMessage AS FailedMessage,
        NULL AS InsertedCount,
        NULL AS UpdatedCount,
        NULL AS DeletedCount,
        @incDurationMS AS DurationMS,
        InsertedCount AS TotalInsertedCount,
        UpdatedCount AS TotalUpdatedCount,
        DeletedCount AS TotalDeletedCount,
        OperationDurationMS AS TotalDurationMS,
        FailedCount AS TotalFailedCount,
        ReworkAttemptCount AS ReworkAttemptCount,
        OperationPriority AS TransformPriority
    FROM AnalyticsInternal.tbl_Batch b WITH (ROWLOCK)
    WHERE PartitionId = @partitionId
        AND BatchId = @batchId

END

GO

