/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AFEB5FF3A845471AB2A6663A26EC304B2AA52744
CREATE PROCEDURE AnalyticsInternal.prc_GetLatestDataQualityResults
    @partitionId INT
AS
BEGIN
    SELECT  PartitionId,
            RunDate,
            StartDate,
            EndDate,
            Name,
            TargetTable,
            ExpectedValue,
            ActualValue,
            Failed,
            KpiValue,
            RunEndDate,
            Scope
    FROM    AnalyticsInternal.tbl_DataQualityResult
    WHERE   PartitionId = @PartitionId
            AND Latest = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
END

GO

