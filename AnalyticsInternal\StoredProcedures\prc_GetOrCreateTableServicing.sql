/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AF872AE3400632E1A643821196A59D15ED696F3C
CREATE PROCEDURE AnalyticsInternal.prc_GetOrCreateTableServicing
   @tableName NVARCHAR(255),
   @partitionScheme NVARCHAR(255),
   @ddlCmd NVARCHAR(MAX),
   @maintenanceId INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    SELECT @maintenanceId = TableMaintenanceId FROM AnalyticsInternal.tbl_TableMaintenance WHERE TableName=@tableName AND Operation = 'SERVICING' AND IsActive = 1

    IF @maintenanceId IS NULL
    BEGIN
        DECLARE @fullTableName VARCHAR(50) = 'AnalyticsModel.' + @tableName
        DECLARE @tempTableName VARCHAR(50) = @fullTableName + '_Temp'
        PRINT 'First attempt ' + @tableName
        INSERT INTO AnalyticsInternal.tbl_TableMaintenance
        SELECT  @tableName,
                'SERVICING',
                0,
                2147483647,
                1 AS IsActive,
                'Servicing for table: ' + @tableName,
                SYSUTCDATETIME()

        SELECT @maintenanceId = TableMaintenanceId FROM AnalyticsInternal.tbl_TableMaintenance WHERE TableName=@tableName AND Operation = 'SERVICING' AND IsActive = 1

        IF OBJECT_ID(@tempTableName) IS NULL
        BEGIN

           EXEC AnalyticsInternal.prc_CloneTable
                @tableName = @fullTableName,
                @newTableName = @tempTableName,
                @partitionScheme = @partitionScheme,
                @stripIdentityAttribute = 0

           IF @ddlCmd IS NOT NULL
           BEGIN
                EXEC(@ddlCmd)
           END
        END
    END
    ELSE
    BEGIN
        PRINT 'Retrying ' + @tableName
    END

    SELECT @maintenanceId

    RETURN 0
END

GO

