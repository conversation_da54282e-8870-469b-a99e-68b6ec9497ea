/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 742AF17EAA4D474CABD4730DFC84CBDB724DB46D
CREATE PROCEDURE AnalyticsInternal.prc_GetOverlapPerSegmentsInPartition
-- Add the parameters for the stored procedure here
@partitionNumber INT = NULL,
@tableName NVARCHAR(128),
@columnName NVARCHAR(128)
AS
BEGIN

    DECLARE @total INT = 1024*1024;
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON;

    DROP TABLE IF EXISTS #AllSegmentsFilteredByTrimReason ;
    -- Insert statements for procedure here

    CREATE TABLE #AllSegmentsFilteredByTrimReason
    (
      partition_number INT,
      min_data_id BIGINT,
      max_data_id BIGINT,
      segment_id INT,
      function_id INT,
      trim_reason_desc NVARCHAR(128),
      row_group_id INT,
      total_rows BIGINT,
      prev_trim_reason_desc NVARCHAR(128),
      row_id INT PRIMARY KEY CLUSTERED
    );

    -- Get a list of all segments with attributes we need
    WITH AllSegments
    AS
    (
        SELECT p.partition_number,
               min_data_id,
               max_data_id,
               segment_id,
               function_id,
               d.total_rows,
               d.trim_reason_desc,
               d.row_group_id
        FROM   sys.column_store_segments s
        JOIN   sys.partitions p
        ON     s.partition_id = p.partition_id
        JOIN   sys.index_columns sic
        ON     sic.index_column_id = s.column_id
               AND sic.object_id = p.object_id
               AND sic.index_id = p.index_id
        JOIN   sys.columns sc
        ON     sic.column_id = sc.column_id
               AND sic.object_id = sc.object_id
        JOIN   sys.indexes i
        ON     sic.object_id = i.object_id
               AND sic.index_id = i.index_id
        JOIN   sys.partition_schemes ps
        ON     ps.data_space_id = i.data_space_id
        JOIN   sys.dm_db_column_store_row_group_physical_stats AS d
        ON     i.object_id = d.object_id
        AND    i.index_id = d.index_id
        AND    s.segment_id = d.row_group_id
        AND    d.partition_number = p.partition_number
        WHERE  sc.name = @columnName
        AND    sc.object_id=OBJECT_ID(@tableName)
        AND    ((@partitionNumber IS NULL)
               OR (p.partition_number = @partitionNumber))
    ),
    -- Collect information of previous row's trim reason
    AllSegmentsWithPrevTrimReason AS
    (
        SELECT a.*,
               LAG(a.trim_reason_desc) OVER (ORDER BY a.row_group_id) prev_trim_reason_desc
        FROM   AllSegments a
     ),
    -- Filter out rows with no trim or bulkload which is not preceded by dictionary_size or memory_limitation trim reasons ,
    -- Also, generate new row_id field to have continuous ordering and to enable join for the next step
    AllSegmentsFilteredByTrimReason AS
       (
        SELECT a.*,
               ROW_NUMBER() OVER (ORDER BY a.row_group_id) row_id
        FROM   AllSegmentsWithPrevTrimReason a
        WHERE  (trim_reason_desc <> 'NO_TRIM')
               OR (trim_reason_desc = 'BULKLOAD' AND prev_trim_reason_desc = 'DICTIONARY_SIZE')
               OR (trim_reason_desc = 'BULKLOAD' AND prev_trim_reason_desc = 'MEMORY_LIMITATION')
       )
    INSERT
    INTO #AllSegmentsFilteredByTrimReason
    (
         partition_number,
         min_data_id,
         max_data_id,
         segment_id,
         function_id,
         trim_reason_desc,
         row_group_id ,
         total_rows,
         prev_trim_reason_desc,
         row_id
    )
    SELECT
         a.partition_number,
         a.min_data_id,
         a.max_data_id,
         a.segment_id,
         a.function_id,
         a.trim_reason_desc,
         a.row_group_id ,
         a.total_rows,
         a.prev_trim_reason_desc,
         a.row_id
    FROM   AllSegmentsFilteredByTrimReason a;

    -- Using the filtered data from above written to a temp table , we use a recursive CTE to get contiguous segments merged together
    -- Segments that add up to 1048576 (or lesser) are actually the same segment group and should be treated as one row while considering overlaps
    -- To identify and merge these we calculate a field called GroupNumber using another calculation of RunningTotal
    -- When RunningTotal exceeds 1048576 , it means current group has finished , hence increment GroupNumber by 1
    -- Also, when a new group is formed , the RunningTotal needs to be reset to be the current value of total_rows and ignore previous rows total so far
    WITH AllSegmentsGroupedByTotal AS
    (
        SELECT min_data_id,
               max_data_id,
               function_id,
               partition_number,
               row_id,
               total_rows,
               1 AS GroupNumber,
               total_rows AS RunningTotal
        FROM   #AllSegmentsFilteredByTrimReason
        WHERE  row_id = 1
        UNION ALL
        SELECT a.min_data_id,
               a.max_data_id,
               a.function_id,
               a.partition_number,
               a.row_id,
               a.total_rows,
               CASE WHEN AllSegmentsGroupedByTotal.RunningTotal +  a.total_rows > @total
                    THEN AllSegmentsGroupedByTotal.GroupNumber + 1
                    ELSE AllSegmentsGroupedByTotal.GroupNumber
               END,
               a.total_rows +
               CASE WHEN AllSegmentsGroupedByTotal.RunningTotal + a.total_rows > @total
                    THEN 0
                    ELSE AllSegmentsGroupedByTotal.RunningTotal
               END
        FROM   #AllSegmentsFilteredByTrimReason a
        JOIN   AllSegmentsGroupedByTotal
               ON a.row_id = AllSegmentsGroupedByTotal.row_id + 1
    ),
    -- Group the segments by the above generated GroupNumber field and also get min_data_id and max_data_id for the groups
    -- Generate RowNumber field along with count and total segments in partition
    GroupedSegments AS
    (
        SELECT MIN(min_data_id) as min_data_id,
               MAX(max_data_id)  as max_data_id,
               partition_number,function_id,
               COUNT(*) as cnt,
               SUM(total_rows) as total,
               ROW_NUMBER() OVER(ORDER BY GroupNumber ASC) AS RowNumber
        FROM AllSegmentsGroupedByTotal
        GROUP by GroupNumber , function_id , partition_number
    ),
    Segments
    AS
    (
        SELECT * ,
               MAX(RowNumber) OVER (ORDER BY RowNumber DESC) AS SegmentsInPartition
        FROM   GroupedSegments
    ),
    -- Get the overlapping segments
    OverlappingSegments
    AS
    (
        SELECT S1.*
        FROM   Segments S1
        JOIN   Segments S2
        ON     S1.partition_number = S2.partition_number
               AND S1.RowNumber < S2.RowNumber
               AND S1.max_data_id BETWEEN S2.min_data_id AND S2.max_data_id
    ),
    PartitionOverlaps
    AS
    (
        SELECT function_id,
               partition_number,
               COUNT(DISTINCT RowNumber) AS Overlaps,
               MAX(SegmentsInPartition) AS SegmentsInPartition
        FROM   OverlappingSegments
        GROUP BY function_id, partition_number
    )

    SELECT DB_NAME() AS DBName,
           partition_number,
           CAST(r.value AS int) AS partitionId,
           Overlaps,
           SegmentsInPartition,
           @tableName as TableName
    FROM   PartitionOverlaps po
    JOIN   sys.partition_range_values r
    ON     po.partition_number = r.boundary_id
           AND po.function_id = r.function_id
    OPTION (MAXRECURSION 30000)
END

GO

