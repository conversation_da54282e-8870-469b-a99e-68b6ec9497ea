/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 7452ACFD7C1239683024A879CDCD772BCFD9D89D
CREATE PROCEDURE AnalyticsInternal.prc_GetPermissionAcl
    @namespaceGuid UNIQUEIDENTIFIER,
    @allowPermission INT,
    @denyPermission INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    SELECT
        a.PartitionId,
        ServiceHostId,
        NamespaceGuid,
        SecurityToken,
        TeamFoundationId,
        AllowPermission,
        DenyPermission
    FROM [dbo].[tbl_SecurityAccessControlEntry] a
    JOIN [dbo].[tbl_DatabasePartitionMap] m ON a.PartitionId = m.PartitionId
    WHERE a.PartitionId > 0 --for devfabric
    AND NamespaceGuid = @namespaceGuid
    AND (AllowPermission = @allowPermission OR DenyPermission = @denyPermission)
END

GO

