/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A9B49222FEC5381EB149207F6E5D0BB1158229DF
CREATE PROCEDURE AnalyticsInternal.prc_iAreMLServicesAvailable
    @areMLServicesAvailable     BIT     OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  OFF

    DECLARE @status INT

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    BEGIN TRY
        EXECUTE sp_execute_external_script
        @language = N'R',
        @script = N'
        a <- 1'
        SET @areMLServicesAvailable = 1
    END TRY
    BEGIN CATCH
        SET @areMLServicesAvailable = 0
    END CATCH

    RETURN 0
END

GO

