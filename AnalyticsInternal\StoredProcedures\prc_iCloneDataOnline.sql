/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: C35F424320264F05DAC8DA0C08010BBCD185D447
CREATE PROCEDURE AnalyticsInternal.prc_iCloneDataOnline
   @maintenanceId INT,
   @tableName NVARCHAR(255),
   @columnDefinitions AnalyticsInternal.typ_ColumnServicingDefinition READONLY
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    SET DEADLOCK_PRIORITY HIGH -- Maintenance is expensive in case of deadlock kill transform that will be retried

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(10)+CHAR(13) -- CR+LF
    DECLARE @indexName NVARCHAR(256)
    DECLARE @mainTableName NVARCHAR(256)
    DECLARE @transformTableName NVARCHAR(256)
    DECLARE @applyMaxBatchFilter NVARCHAR(256)
    DECLARE @keyColumn NVARCHAR(256)
    DECLARE @matchPredicate NVARCHAR(256)
    DECLARE @hasIdentity BIT
    DECLARE @optimizeBatchCalculation BIT

    SELECT @tableName, @maintenanceId

    SELECT  @mainTableName = TableName,
            @applyMaxBatchFilter = ApplyMaxBatchFilter,
            @keyColumn = KeyColumn,
            @hasIdentity = HasIdentity,
            @matchPredicate = MatchPredicate,
            @indexName= IndexName,
            @transformTableName  = TransformTableName,
            @optimizeBatchCalculation = OptimizeBatchCalculation
    FROM    AnalyticsInternal.func_iGetTableMaintenanceDefinitions()
    WHERE   TableName = @tableName

    DECLARE @allFields NVARCHAR(MAX) = ''
    DECLARE @allFieldsWithPrefix NVARCHAR(MAX) = ''
    DECLARE @allFieldsWithTargetPrefix NVARCHAR(MAX) = ''
    DECLARE @updateFields NVARCHAR(MAX) = ''

    EXEC AnalyticsInternal.prc_iGenerateCloneFieldsExpressions     @mainTableName,
                                                                   @columnDefinitions,
                                                                   @allFields OUTPUT,
                                                                   @allFieldsWithPrefix OUTPUT,
                                                                   @allFieldsWithTargetPrefix OUTPUT,
                                                                   @updateFields OUTPUT

    DECLARE @currentPhase VARCHAR(20) = 'Online Clone'
    DECLARE @nextPhase VARCHAR(20) = 'Offline Merge Recent'

    DECLARE @onlineJoin NVARCHAR(MAX) = IIF(@applyMaxBatchFilter = 1, N'
        (SELECT ProcessPartitionId AS PartitionId, LastBatchId FROM AnalyticsInternal.tbl_TableMaintenancePartition WHERE Phase = @currentPhase AND TableMaintenanceId = @maintenanceId) b INNER HASH JOIN
        ',
        '')

    DECLARE @onlineFilter NVARCHAR(MAX) = IIF(@applyMaxBatchFilter = 1, N'
          ON b.PartitionId = s.PartitionId AND s.AnalyticsBatchId < b.LastBatchId',
          '')

    DECLARE @copyCommand NVARCHAR(MAX) = N'
    DECLARE @startPartition INT
    DECLARE @endPartition   INT
    DECLARE @lastEndSK BIGINT

    '
    + IIF(@hasIdentity = 1, 'SET IDENTITY_INSERT ' + @mainTableName +'_Temp ON','')
    +'
    WHILE EXISTS (SELECT StartPartitionId, EndPartitionId
       FROM AnalyticsInternal.tbl_TableMaintenancePartition
       WHERE Phase = @currentPhase
       AND TableMaintenanceId = @maintenanceId
       GROUP BY StartPartitionId, EndPartitionId)
    BEGIN
       SELECT TOP 1 @startPartition = StartPartitionId, @endPartition = EndPartitionId, @lastEndSK = MAX(LastSK)
       FROM AnalyticsInternal.tbl_TableMaintenancePartition
       WHERE Phase = @currentPhase
       AND TableMaintenanceId = @maintenanceId
       GROUP BY StartPartitionId, EndPartitionId
       ORDER BY StartPartitionId

       DECLARE @batchSize INT = 1024*1024
       DECLARE @batchStartSK BIGINT = 0
       DECLARE @batchEndSK BIGINT
       DECLARE @maxSK BIGINT
       DECLARE @minSK BIGINT
       DECLARE @minCount INT = 1024 * 100

       -- TESTPATTERN: INLOOP

       SELECT   @maxSK = MAX(' + @keyColumn +'),
                @minSK = MIN(' + @keyColumn +')
       FROM '
       + @onlineJoin +
       + @mainTableName +' s
       ' + @onlineFilter +'
       WHERE s.PartitionId BETWEEN @startPartition AND @endPartition

       SET @batchStartSK = @minSK
       SET @batchEndSK = @lastEndSK

       WHILE @batchStartSK <= @maxSK
       BEGIN
          IF @batchEndSK IS NOT NULL
          BEGIN
              SET @batchStartSK = @batchEndSK + 1
          END
          '
          + IIF( @optimizeBatchCalculation = 1,
          -- For really big tables, creating exact 1M partitions due to sorting
          -- Just adding 1M to previous SK and ensuring that we are getting enough records to write them directly to columnstore
          'DECLARE @candidateEndSK BIGINT = @batchStartSK
          DECLARE @countInBatch INT = 0

          WHILE 1 = 1
          BEGIN
              SET @candidateEndSK += @batchSize
              SELECT  @batchEndSK =  MAX(' + @keyColumn +'), @countInBatch =  COUNT(' + @keyColumn +')
                            FROM
                            (
                                SELECT  ' + @keyColumn +'
                                FROM    '
                                + @onlineJoin +
                                + @mainTableName +' s WITH (INDEX (' + @indexName +'))
                                ' + @onlineFilter +'
                                WHERE   s.PartitionId BETWEEN @startPartition AND @endPartition
                                        AND ' + @keyColumn +' >= @batchStartSK
                                        AND ' + @keyColumn +' < @candidateEndSK
                            ) T
                            OPTION (RECOMPILE)
               -- Exit if we have enough records or reached the end of the data
               IF @countInBatch > @minCount OR @candidateEndSK > @maxSK
               BEGIN
                  BREAK
               END

               -- Sleep if using too much CPU
               EXEC prc_iSleepIfBusy

          END'
          ,
          '
              SELECT  @batchEndSK =  MAX(' + @keyColumn +')
                            FROM
                            (
                                  SELECT   TOP(@batchSize) ' + @keyColumn +'
                                  FROM     '
                                  + @onlineJoin +
                                  + @mainTableName +' s WITH (INDEX (' + @indexName +'))
                                  ' + @onlineFilter +'
                                  WHERE    s.PartitionId BETWEEN @startPartition AND @endPartition
                                           AND ' + @keyColumn +' >= @batchStartSK
                                  ORDER BY ' + @keyColumn +'
                            ) T
                            OPTION (RECOMPILE)
          ')
          +'
          INSERT INTO ' + @mainTableName +'_Temp
          (
          ' + @allFields +'
          )
          SELECT
          ' + @allFieldsWithPrefix +'
          FROM '
          + @onlineJoin +
          + @mainTableName +' s WITH(INDEX(' + @indexName +'))
          ' + @onlineFilter +'
          WHERE s.PartitionId BETWEEN @startPartition AND @endPartition
                AND ' + @keyColumn +' BETWEEN @batchStartSK AND @batchEndSK
          ORDER BY ' + @keyColumn +'
          OPTION (OPTIMIZE FOR (@startPartition UNKNOWN, @endPartition UNKNOWN))

          -- Save LastSK to restart in case of disconnect
          UPDATE AnalyticsInternal.tbl_TableMaintenancePartition
          SET LastSK = ISNULL(@batchEndSK, LastSK)
          WHERE StartPartitionId = @startPartition
          AND TableMaintenanceId = @maintenanceId

          -- Sleep if using too much CPU
          EXEC prc_iSleepIfBusy
       END

       UPDATE AnalyticsInternal.tbl_TableMaintenancePartition
       SET Phase = @nextPhase, LastSK = NULL
       WHERE StartPartitionId = @startPartition
       AND TableMaintenanceId = @maintenanceId
    END'

    -- Debug output
    SELECT @copyCommand AS Command FOR XML PATH

    BEGIN TRY
        EXEC sp_executesql @copyCommand
        ,N'@maintenanceId INT, @currentPhase VARCHAR(20), @nextPhase VARCHAR(20)'
        , @maintenanceId = @maintenanceId
        , @currentPhase = @currentPhase
        , @nextPhase = @nextPhase
    END TRY
    BEGIN CATCH
       -- When failed to copy => exit to avoid data loss
       SET @errorMessage = ERROR_MESSAGE()
       SET @status = 1670017
       SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @mainTableName, @errorMessage)
       RETURN -1
    END CATCH

    RETURN 0
END

GO

