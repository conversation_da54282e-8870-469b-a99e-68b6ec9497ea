/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B6C20131C54DF5A10B4BEB86294A47AF62F1D4F4
CREATE PROCEDURE AnalyticsInternal.prc_iCreateBatch
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId        INT,
    @operation VARCHAR(10),
    @operationSproc VARCHAR(100),
    @operationSprocVersion INT,
    @operationPriority INT,
    @operationTriggerTableName VARCHAR(64),
    @operationTriggerBatchIdStart BIGINT,
    @operationTriggerBatchIdEnd BIGINT,
    @batchDt DATETIME,
    @newBatchId BIGINT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    EXEC @status = prc_iCounterGetNext @partitionId = @partitionId, @counterName = 'AnalyticsBatchIdCounter', @countToReserve = 1, @firstIdToUse = @newBatchId OUTPUT

    IF (@status <> 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670005); RAISERROR(@tfError, 16, -1, @procedureName, 'AnalyticsBatchIdCounter', @status)
        RETURN 1670005
    END

    INSERT AnalyticsInternal.tbl_Batch WITH (ROWLOCK)
        (
        PartitionId,
        BatchId,
        TableName,
        AnalyticsProviderShardId,
        AnalyticsStreamId,
        Operation,
        OperationSproc,
        OperationSprocVersion,
        OperationPriority,
        OperationActive,
        Ready,
        Failed,
        AttemptCount,
        CreateDateTime,
        OperationTriggerTableName,
        OperationTriggerBatchIdStart,
        OperationTriggerBatchIdEnd,
        Invalidated
        )
    SELECT @partitionId,
        @newBatchId,
        @tableName,
        @providerShardId,
        @streamId,
        @operation,
        @operationSproc,
        @operationSprocVersion,
        @operationPriority,
        0 AS Active,
        0 AS Ready,
        0 AS Failed,
        NULL AS AttemptCount,
        @batchDt,
        @operationTriggerTableName,
        @operationTriggerBatchIdStart,
        @operationTriggerBatchIdEnd,
        IIF(@operation = 'reprocess', 1, 0)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    RETURN @status
END

GO

