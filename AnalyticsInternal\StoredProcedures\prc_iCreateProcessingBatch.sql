/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 96D5EA8759643122C5E25E9FF952EF3A66BF9A26
CREATE PROCEDURE AnalyticsInternal.prc_iCreateProcessingBatch
    @partitionId    INT,
    @tableName      VARCHAR(64),
    @minPriority    INT,
    @settings       typ_KeyValuePairStringTable READONLY,
    @newBatchId     BIGINT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    -- this is here to ensure defintion changes are reflected in the TransformState table
    -- could be done as host level servicing to avoid this call
    -- but the definitionsOnly option should be quick
    EXEC AnalyticsInternal.prc_iEnsureTransformState @partitionId, @definitionsOnly = 1

    DECLARE @now DATETIME = GETUTCDATE()

    CREATE TABLE #Work
    (
        PartitionId INT,
        BatchId BIGINT,
        KnownTriggerBatchIdStart BIGINT,
        TriggerBatchIdStart BIGINT,
        TriggerBatchIdEnd BIGINT,
        TriggerTableName VARCHAR(64) COLLATE DATABASE_DEFAULT,
        TargetTableName VARCHAR(64) COLLATE DATABASE_DEFAULT,
        TargetOperation VARCHAR(10) COLLATE DATABASE_DEFAULT,
        SprocName VARCHAR(256) COLLATE DATABASE_DEFAULT,
        SprocVersion INT,
        OperationState VARCHAR(10) COLLATE DATABASE_DEFAULT,
        OperationStateData BIGINT,
        OperationScope VARCHAR(10) COLLATE DATABASE_DEFAULT,
        TransformOrder INT,
        TransformPriority INT,
        ReworkAttemptCount INT,
        Held BIT,
        IncludesKnownTriggerBatches AS IIF(TriggerBatchIdEnd >= KnownTriggerBatchIdStart, 1, 0) PERSISTED,
        IsReworkPriority AS IIF(TransformPriority <= 1, 1, 0) PERSISTED,
        IsRework AS IIF(ReworkAttemptCount > 0, 1, 0) PERSISTED
    )

    -- generate all incremental work from triggers
    INSERT INTO #Work
    SELECT t.PartitionId,
        NULL AS BatchId,
        ISNULL(t.TriggerBatchIdMin, t.TriggerBatchIdMax) AS KnownTriggerBatchIdStart,
        ISNULL(t.DoingBatchIdEnd, 0) + 1 AS TriggerBatchIdStart,
        t.TriggerBatchIdMax AS TriggerBatchIdEnd,
        t.TriggerTableName,
        t.TargetTableName,
        t.TargetOperation,
        t.SprocName,
        t.SprocVersion,
        NULL, --OperationState
        NULL, --OperationStateData
        t.OperationScope,
        t.TransformOrder,
        t.TransformPriority,
        NULL AS ReworkAttemptCount,
        t.Hold
    FROM AnalyticsInternal.tbl_TransformState t WITH (READPAST)
    WHERE PartitionId = @partitionId
        AND ISNULL(t.DoingBatchIdEnd, 0) < t.TriggerBatchIdMax
        AND t.TransformPriority >= @minPriority
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- generate all interval work
    INSERT INTO #Work
    SELECT t.PartitionId,
        NULL AS BatchId,
        NULL AS KnownTriggerBatchIdStart,
        -1 AS TriggerBatchIdStart,
        -1 AS TriggerBatchIdEnd,
        t.TriggerTableName,
        t.TargetTableName,
        t.TargetOperation,
        t.SprocName,
        t.SprocVersion,
        NULL, --OperationState
        NULL, --OperationStateData
        t.OperationScope,
        t.TransformOrder,
        t.TransformPriority,
        NULL AS ReworkAttemptCount,
        t.Hold
    FROM AnalyticsInternal.tbl_TransformState t WITH (READPAST)
    WHERE PartitionId = @partitionId
        AND @now >= DATEADD(second,
                            ABS(HASHBYTES('SHA2_256', CONCAT(@partitionId, t.SprocName)) % (IntervalMinutes * 60)), -- random distribution across interval, based on partitionId and SprocName
                            DATEADD(minute, ((DATEDIFF(minute, 0, ISNULL(DoingBatchCreateDateTime, TransformDefInstallDate)) / IntervalMinutes) + 1) * IntervalMinutes, 0) -- end of interval for last run
                            ) -- scheduled execution time
        AND t.TransformPriority >= @minPriority
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF 1 >= @minPriority
    BEGIN
        -- generate all rework from triggeres
        INSERT INTO #Work
        SELECT t.PartitionId,
            NULL AS BatchId,
            0 AS KnownTriggerBatchIdStart,
            0 AS TriggerBatchIdStart,
            t.ReTriggerBatchIdMax AS TriggerBatchIdEnd,
            t.TriggerTableName,
            t.TargetTableName,
            t.TargetOperation,
            t.SprocName,
            t.SprocVersion,
            NULL, --OperationState
            NULL, --OperationStateData
            t.OperationScope,
            t.TransformOrder,
            1,
            1 AS ReworkAttemptCount,
            t.Hold
        FROM AnalyticsInternal.tbl_TransformState t WITH (READPAST)
        WHERE PartitionId = @partitionId
            AND ISNULL(t.ReDoingBatchIdEnd, 0) < t.ReTriggerBatchIdMax
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- from requested rework table
        INSERT INTO #Work
        SELECT t.PartitionId,
            NULL AS BatchId,
            t.TriggerBatchIdStart AS KnownTriggerBatchIdStart,
            t.TriggerBatchIdStart,
            t.TriggerBatchIdEnd,
            t.TriggerTableName,
            t.TargetTableName,
            t.TargetOperation,
            t.SProcName,
            ts.SprocVersion,
            t.State,
            t.StateData,
            ts.OperationScope,
            ts.TransformOrder,
            1,
            t.ReworkAttemptCount,
            ts.Hold
        FROM AnalyticsInternal.tbl_TransformRework t WITH (READPAST)
        JOIN AnalyticsInternal.tbl_TransformState ts WITH (READPAST)
            ON t.PartitionId = ts.PartitionId
            AND t.TriggerTableName = ts.TriggerTableName
            AND t.TargetTableName = ts.TargetTableName
            AND t.TargetOperation = ts.TargetOperation
            AND t.SprocName = ts.SProcName
        WHERE t.PartitionId = @partitionId
            AND ISNULL(t.ScheduledDateTime, t.CreateDateTime) <= @now
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    IF EXISTS (SELECT * FROM #Work)
    BEGIN
        -- delete work that is feature flag disabled.
        -- this approach will not respect any dependency blocking on disable feautures.
        -- DO ensure feature flagged transforms have no downstream dependencies not on the same flag
        DELETE w
        FROM #Work w
        JOIN @settings s
            ON s.[Key] = CONCAT('/Service/Analytics/Settings/Transform/', w.SprocName, '/', 'FeatureDisabled')

        -- add pending work already in batches but not complete - for predicate detection
        INSERT INTO #Work
        SELECT b.PartitionId,
            b.BatchId AS BatchId,
            b.OperationTriggerBatchIdStart AS KnownTriggerBatchIdStart,
            b.OperationTriggerBatchIdStart AS TriggerBatchIdStart,
            b.OperationTriggerBatchIdEnd AS TriggerBatchIdEnd,
            b.OperationTriggerTableName AS TriggerTableName,
            b.TableName AS TargetTableName,
            b.Operation AS TargetOperation,
            b.OperationSproc AS SprocName,
            ts.SprocVersion,
            NULL, --OperationState
            NULL, --OperationStateData
            ts.OperationScope,
            ts.TransformOrder,
            b.OperationPriority,
            b.ReworkAttemptCount,
            ts.Hold
        FROM AnalyticsInternal.tbl_Batch b WITH (NOLOCK, INDEX (IX_AnalyticsInternal_tbl_Batch_NotReadyNotFailedProcess)) -- I want to include locked batch rows
        INNER LOOP JOIN AnalyticsInternal.tbl_TransformState ts WITH (READPAST)
            ON b.PartitionId = ts.PartitionId
            AND b.OperationTriggerTableName = ts.TriggerTableName
            AND b.TableName = ts.TargetTableName
            AND b.Operation = ts.TargetOperation
            AND b.OperationSproc = ts.SProcName
        WHERE b.PartitionId = @partitionId
            AND b.OperationTriggerTableName IS NOT NULL
            AND b.Failed = 0
            AND b.Ready = 0
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1) -- stats on this filtered index are not trustworthy

        -- don't transform incremental source batch until predicate defs have transformed the batch
        -- clip work with predicate
        -- only apply to incremental, as clipping rework will prevent correct acknowledgement of batch correction
        UPDATE work
        SET TriggerBatchIdEnd = ISNULL(preds.TriggerBatchIdStartMin - 1, TriggerBatchIdEnd)
        FROM #Work work
        OUTER APPLY
        (
            SELECT MIN(pred.TriggerBatchIdStart) AS TriggerBatchIdStartMin
            FROM #Work pred
            INNER LOOP JOIN AnalyticsInternal.tbl_TransformDefinitionPredicate preddef
                ON pred.PartitionId = work.PartitionId
                AND pred.OperationScope = 'batch'
                AND pred.TriggerTableName = preddef.PredicateTriggerTableName
                AND pred.SprocName = preddef.PredicateSprocName
                AND pred.TriggerBatchIdStart <= work.TriggerBatchIdEnd
                AND pred.IsReworkPriority = 0
            WHERE preddef.TriggerTableName = work.TriggerTableName
                AND preddef.SprocName = work.SprocName
        ) preds
        WHERE work.PartitionId = @partitionId AND work.OperationScope = 'batch' AND work.IsRework = 0
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- remove work that was clipped to null
        DELETE work
        FROM #Work work
        WHERE PartitionId = @partitionId AND TriggerBatchIdStart > TriggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- remove work with predicate: batch following by transform def order
        -- only applies to rework, because rework was skipped on previous step
        DELETE work
        FROM #Work work
        JOIN #Work pred
            ON pred.PartitionId = work.PartitionId
            AND pred.OperationScope = 'batch'
            AND pred.TriggerTableName = work.TriggerTableName
            AND pred.TriggerBatchIdStart <= work.TriggerBatchIdEnd
            AND pred.TransformOrder < work.TransformOrder
        WHERE work.PartitionId = @partitionId AND work.OperationScope = 'batch' AND work.IsRework = 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- remove work with predicate: batch following table op
        DELETE work
        FROM #Work work
        JOIN #Work pred
            ON pred.PartitionId = work.PartitionId
            AND pred.OperationScope = 'table'
            AND pred.TriggerTableName = work.TriggerTableName
            AND pred.TriggerBatchIdStart < work.TriggerBatchIdStart
            AND pred.IsReworkPriority = work.IsReworkPriority
        WHERE work.PartitionId = @partitionId AND work.OperationScope = 'batch'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- remove work with predicate: table op following batch op
        DELETE work
        FROM #Work work
        JOIN #Work pred
            ON pred.PartitionId = work.PartitionId
            AND pred.OperationScope = 'batch'
            AND pred.TriggerTableName = work.TriggerTableName
            AND pred.TriggerBatchIdStart < work.TriggerBatchIdStart
            AND pred.IsReworkPriority = work.IsReworkPriority
        WHERE work.PartitionId = @partitionId AND work.OperationScope = 'table'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- remove work with predicate: table op following table op by batch or transform def
        -- table scope ops may interfere with each other
        DELETE work
        FROM #Work work
        JOIN #Work pred
            ON pred.PartitionId = work.PartitionId
            AND pred.OperationScope = 'table'
            AND pred.TriggerTableName = work.TriggerTableName
            AND (
                pred.TriggerBatchIdStart < work.TriggerBatchIdStart
                OR (pred.TriggerBatchIdStart = work.TriggerBatchIdStart AND pred.TransformOrder < work.TransformOrder)
                )
            AND pred.IsReworkPriority = work.IsReworkPriority
        WHERE work.PartitionId = @partitionId AND work.OperationScope = 'table'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        DECLARE @targetTableName VARCHAR(64)
        DECLARE @targetOperation  VARCHAR(10)
        DECLARE @targetOperationScope VARCHAR(10)
        DECLARE @sprocName  VARCHAR(256)
        DECLARE @sprocVersion INT
        DECLARE @operationPriority INT
        DECLARE @operationState VARCHAR(10)
        DECLARE @operationStateData BIGINT
        DECLARE @newBatchDt DATETIME = @now
        DECLARE @triggerTableName VARCHAR(64)
        DECLARE @triggerBatchIdStart BIGINT
        DECLARE @triggerBatchIdEnd BIGINT
        DECLARE @reworkAttemptCount INT
        DECLARE @isRework BIT

        -- choose a table and operation
        -- put batches at the end that aren't known to include real work - due to predicate clipping
        SELECT TOP 1 @targetTableName = TargetTableName,
            @targetOperation = TargetOperation,
            @targetOperationScope = OperationScope,
            @sprocName = SprocName,
            @sprocVersion = SprocVersion,
            @operationPriority = TransformPriority,
            @operationState = OperationState,
            @operationStateData = OperationStateData,
            @triggerTableName = TriggerTableName,
            @triggerBatchIdStart = TriggerBatchIdStart,
            @triggerBatchIdEnd = TriggerBatchIdEnd,
            @reworkAttemptCount = ReworkAttemptCount,
            @isRework = IsRework
        FROM #Work
        WHERE BatchId IS NULL
            AND Held = 0
        ORDER BY TransformPriority DESC, IncludesKnownTriggerBatches DESC, TriggerBatchIdStart ASC, TransformOrder ASC

        IF (@@ROWCOUNT > 0)
        BEGIN
            BEGIN TRAN

            EXEC AnalyticsInternal.prc_iCreateBatch
                @partitionId,
                @targetTableName,
                NULL,
                NULL,
                @targetOperation,
                @sprocName,
                @sprocVersion,
                @operationPriority,
                @triggerTableName,
                @triggerBatchIdStart,
                @triggerBatchIdEnd,
                @newBatchDt,
                @newBatchId = @newBatchId OUTPUT

            IF (@operationState IS NOT NULL OR @operationStateData IS NOT NULL OR @reworkAttemptCount IS NOT NULL)
            BEGIN
                UPDATE AnalyticsInternal.tbl_Batch WITH (ROWLOCK)
                SET OperationState = @operationState,
                    OperationStateData = @operationStateData,
                    ReworkAttemptCount = @reworkAttemptCount
                WHERE PartitionId = @partitionId
                    AND BatchId = @newBatchId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            END

            -- batch created - move doing marker in transform state
            UPDATE t
            SET t.DoingBatchIdEnd = IIF(@isRework = 0, @triggerBatchIdEnd, t.DoingBatchIdEnd),
                t.DoingBatchCreateDateTime = @newBatchDt,
                t.TriggerBatchIdMin = IIF(@triggerBatchIdEnd >= t.TriggerBatchIdMax, NULL, t.TriggerBatchIdMin),  -- clear TriggerBatchIdMin if all known work was batched
                t.ReDoingBatchIdEnd = IIF(@triggerBatchIdStart <= 1, @triggerBatchIdEnd, t.ReDoingBatchIdEnd) -- not using isRework so that initial transform will set ReDoingBatchIdEnd, avoiding immediate rework after initial transform
            FROM AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            WHERE t.PartitionId = @partitionId
                AND t.TriggerTableName = @triggerTableName
                AND t.TargetTableName = @targetTableName
                AND t.TargetOperation = @targetOperation
                AND t.SprocName = @sprocName
                AND t.SprocVersion = @sprocVersion
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            -- batch created - remove from rework list
            DELETE
            FROM    AnalyticsInternal.tbl_TransformRework
            WHERE   PartitionId = @partitionId
                    AND TriggerTableName = @triggerTableName
                    AND TargetTableName = @targetTableName
                    AND TargetOperation = @targetOperation
                    AND SprocName = @sprocName
                    AND SprocVersion <= @sprocVersion
                    AND TriggerBatchIdEnd = @triggerBatchIdEnd

            COMMIT TRAN
        END
    END

    DROP TABLE #Work

END

GO

