/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CF1521A03A60AD5F670C8B30C3FB7B74A19222C7
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelProcess_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 1000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #TeamProcesses
    (
        TeamSK          UNIQUEIDENTIFIER NOT NULL,
        ProjectSK       UNIQUEIDENTIFIER NOT NULL,
        ProcessID       UNIQUEIDENTIFIER NOT NULL,
        BugsBehavior    INT,
        PRIMARY KEY CLUSTERED (TeamSK, ProjectSK, ProcessID)
    )

    IF (@triggerTableName = 'TeamSetting')
    BEGIN
        -- we can avoid a join to the model team table because AnalyticsStage.tbl_TeamSetting.TeamGuid == AnalyticsModel.tbl_Team.TeamSK AND ProjectGuid == ProjectSK
        INSERT  #TeamProcesses (
                TeamSK,
                ProjectSK,
                ProcessID,
                BugsBehavior
        )
        SELECT  sts.TeamGuid,
                sts.ProjectGuid,
                sp.ProcessId,
                sts.BugsBehavior
        FROM    AnalyticsStage.tbl_TeamSetting sts
        JOIN    AnalyticsStage.tbl_Project sp
        ON      sts.PartitionId = sp.PartitionId
                AND sts.ProjectGuid = sp.ProjectGuid
        WHERE   sts.PartitionId = @partitionId
                AND sts.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND sp.ProcessId IS NOT NULL -- some staged records have been observed on SU3 w/null ProcessId (IsDeleted = 1).
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE IF (@triggerTableName = 'Process')
    BEGIN
        -- we can avoid a join to the model project table because AnalyticsStage.tbl_Project.ProjectGuid == AnalyticsModel.tbl_Project.ProjectSK
        INSERT  #TeamProcesses (
                TeamSK,
                ProjectSK,
                ProcessID,
                BugsBehavior
        )
        SELECT  sts.TeamGuid,
                sts.ProjectGuid,
                sp.ProcessId,
                sts.BugsBehavior
        FROM    AnalyticsStage.tbl_Process sp
        JOIN    AnalyticsStage.tbl_Project spj
        ON      sp.PartitionId = spj.PartitionId
                AND sp.ProcessId = spj.ProcessId
        JOIN    AnalyticsStage.tbl_TeamSetting sts
        ON      sp.PartitionId = sts.PartitionId
                AND spj.ProjectGuid = sts.ProjectGuid
        WHERE   sp.PartitionId = @partitionId
                AND sp.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND spj.ProcessId IS NOT NULL -- some staged records have been observed on SU3 w/null ProcessId (IsDeleted = 1).
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE IF (@triggerTableName = 'Project')
    BEGIN
        -- we can avoid a join to the model project table because AnalyticsStage.tbl_Project.ProjectGuid == AnalyticsModel.tbl_Project.ProjectSK
        INSERT  #TeamProcesses (
                TeamSK,
                ProjectSK,
                ProcessID,
                BugsBehavior
        )
        SELECT  sts.TeamGuid,
                sp.ProjectGuid,
                sp.ProcessId,
                sts.BugsBehavior
        FROM    AnalyticsStage.tbl_Project sp
        JOIN    AnalyticsStage.tbl_TeamSetting sts
        ON      sp.PartitionId = sts.PartitionId
                AND sp.ProjectGuid = sts.ProjectGuid
        WHERE   sp.PartitionId = @partitionId
                AND sp.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND sp.ProcessId IS NOT NULL -- some staged records have been observed on SU3 w/null ProcessId (IsDeleted = 1).
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END

    CREATE TABLE #WorkItemTypes
    (
        ProcessId           UNIQUEIDENTIFIER NOT NULL,
        WorkItemType        NVARCHAR(256) COLLATE DATABASE_DEFAULT NOT NULL,
        IsHiddenCategory    BIT,
        IsBugCategory       BIT,
        PRIMARY KEY CLUSTERED (ProcessId, WorkItemType)
    )

    INSERT  #WorkItemTypes (
            ProcessId,
            WorkItemType,
            IsHiddenCategory,
            IsBugCategory
    )
    SELECT  ProcessId,
            WorkItemType,
            MAX(IIF(WorkItemTypeCategoryReferenceName = 'Microsoft.HiddenCategory', 1, 0)) AS IsHiddenCategory,
            MAX(IIF(WorkItemTypeCategoryReferenceName = 'Microsoft.BugCategory', 1, 0)) AS IsBugCategory
    FROM    (
                SELECT  sp.ProcessId,
                        tc.Item.value('ReferenceName[1]', 'nvarchar(256)') AS WorkItemTypeCategoryReferenceName,
                        wi.Item.value('(text()[1])', 'nvarchar(256)') AS WorkItemType,
                        0 AS IsHiddenCategory, -- set in wrapping select which is more performant than addition XML operations
                        0 AS IsBugCategory -- set in wrapping select which is more performant than addition XML operations
                FROM    AnalyticsStage.tbl_Process sp
                CROSS APPLY sp.TypeCategories.nodes('//Item') tc(Item)
                CROSS APPLY tc.Item.nodes('MemberWorkItemTypeNames') wi(Item)
                WHERE   sp.PartitionId = @partitionId
                        AND sp.ProcessId IN (
                            SELECT ProcessId FROM #TeamProcesses
                        )
            ) AS WIT
    GROUP BY ProcessId, -- WorkItemTypes can exist in multiple categories. We want to know if those types exist in the bugs or hidden categories so we perform this group by with the MAX agg functions above to de-dupe types.
            WorkItemType
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    -- workaround - add more types from Fields, as custom types are not necessarily present in type categories
    INSERT  #WorkItemTypes (
            ProcessId,
            WorkItemType,
            IsHiddenCategory,
            IsBugCategory
    )
    SELECT  wi.ProcessId,
            wi.WorkItemType,
            0 AS IsHiddenCategory,
            0 AS IsBugCategory
    FROM    (
            SELECT  ProcessId,
                    WorkItemType
            FROM    (
                        SELECT  sp.ProcessId,
                                wi.Item.value('(text()[1])', 'nvarchar(256)') AS WorkItemType
                        FROM    AnalyticsStage.tbl_Process sp
                        CROSS APPLY sp.Fields.nodes('//Item') tc(Item)
                        CROSS APPLY tc.Item.nodes('WorkItemTypes') wi(Item)
                        WHERE   sp.PartitionId = @partitionId
                                AND sp.ProcessId IN (
                                    SELECT ProcessId FROM #TeamProcesses
                                )
                    ) AS WIT
            GROUP BY ProcessId,
                    WorkItemType
            ) wi
    LEFT OUTER JOIN #WorkItemTypes wicurrent
    ON      wicurrent.ProcessId = wi.ProcessId
            AND wicurrent.WorkItemType = wi.WorkItemType
    WHERE   wicurrent.ProcessId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    CREATE TABLE #Backlogs (
        ProcessId           UNIQUEIDENTIFIER,
        BacklogCategoryReferenceName NVARCHAR(70)   COLLATE DATABASE_DEFAULT,
        BacklogName         NVARCHAR(256)           COLLATE DATABASE_DEFAULT,
        BacklogLevel        INT,
        BacklogType         NVARCHAR(50)            COLLATE DATABASE_DEFAULT,
        WorkItemType        NVARCHAR(256)           COLLATE DATABASE_DEFAULT,
        PRIMARY KEY CLUSTERED (ProcessId, WorkItemType)
    )
    INSERT  #Backlogs (
            ProcessId,
            BacklogCategoryReferenceName,
            BacklogName,
            BacklogLevel,
            BacklogType,
            WorkItemType
    )
    SELECT  sp.ProcessId,
            bl.Item.value('CategoryReferenceName[1]','nvarchar(70)') AS BacklogCategoryReferenceName,
            ISNULL(NULLIF(bl.Item.value('PluralName[1]','nvarchar(256)'), ''), bl.Item.value('SingularName[1]','nvarchar(70)')) AS BacklogName,
            CASE WHEN bl.Item.exist('Level[1]') = 1 THEN bl.Item.value('Level[1]','int') ELSE NULL END AS BacklogLevel,
            bl.Item.value('BacklogType[1]','nvarchar(50)') AS BacklogType,
            wi.Item.value('(text())[1]', 'nvarchar(256)') AS WorkItemType
    FROM    AnalyticsStage.tbl_Process sp
    CROSS APPLY sp.Backlogs.nodes('//Item') bl(Item)
    OUTER APPLY bl.Item.nodes('WorkItemTypeNames') wi(Item)
    WHERE   sp.PartitionId = @PartitionId
            AND sp.ProcessId IN (
                SELECT ProcessId FROM #TeamProcesses
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    CREATE TABLE #Src
    (
        ProcessID                       UNIQUEIDENTIFIER    NOT NULL, -- key
        ProjectSK                       UNIQUEIDENTIFIER    NOT NULL, -- key
        TeamSK                          UNIQUEIDENTIFIER    NOT NULL, -- key
        WorkItemType                    NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL, -- key
        IsHiddenType                    BIT                 NOT NULL,
        IsBugType                       BIT                 NOT NULL,
        HasBacklog                      BIT                 NOT NULL,
        BacklogCategoryReferencename    NVARCHAR(70)        COLLATE DATABASE_DEFAULT NULL,
        BacklogName                     NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        BacklogType                     NVARCHAR(50)        COLLATE DATABASE_DEFAULT NULL,
        BacklogLevel                    INT                 NULL,
        PRIMARY KEY CLUSTERED (ProcessID, ProjectSK, TeamSK, WorkItemType)
    )

    -- Insert team/backlog/work item type records for work item types explicitly mapped to a backlog.
    INSERT #Src (
        ProcessID,
        ProjectSK,
        TeamSK,
        WorkItemType,
        IsHiddenType,
        IsBugType,
        HasBacklog,
        BacklogCategoryReferenceName,
        BacklogName,
        BacklogType,
        BacklogLevel
    )
    SELECT  tp.ProcessID,
            tp.ProjectSK,
            tp.TeamSK,
            wit.WorkItemType,
            wit.IsHiddenCategory,
            wit.IsBugCategory,
            IIF(wit.WorkItemType = bl.WorkItemType, 1, 0) AS HasBacklog,
            bl.BacklogCategoryReferenceName,
            bl.BacklogName,
            bl.BacklogType,
            bl.BacklogLevel
    FROM    #WorkItemTypes wit
    JOIN    #TeamProcesses tp
    ON      tp.ProcessID = wit.ProcessId
    LEFT OUTER JOIN #Backlogs bl
    ON      wit.ProcessId = bl.ProcessId
            AND wit.WorkItemType = bl.WorkItemType

    -- Now update bug types based on team's bugs behavior
    UPDATE  src
    SET     src.BacklogCategoryReferenceName = bl.BacklogCategoryReferenceName,
            src.BacklogName = bl.BacklogName,
            src.BacklogType = bl.BacklogType,
            src.BacklogLevel = bl.BacklogLevel
    FROM    #Src src
    JOIN    (
                -- Get the reference name of the bug backlog for each team based on team's bug behavior.
                SELECT  TeamSK,
                        ProjectSK,
                        ProcessID,
                        BugsBehavior,
                        CASE
                            WHEN BugsBehavior = 1 THEN 'Microsoft.RequirementCategory'
                            WHEN BugsBehavior = 2 THEN 'Microsoft.TaskCategory'
                            ELSE NULL
                        END AS BugsBacklog
                FROM    #TeamProcesses
                WHERE   BugsBehavior > 0 -- BugsBehavior = 0 bugs don't go on any backlogs, and we can skip these records.
            ) bb
    ON      src.ProcessId = bb.ProcessId
            AND src.ProjectSK = bb.ProjectSK
            AND src.TeamSK = bb.TeamSK
    JOIN    (
                -- Get backlog info (without mapped WorkItemTypes)
                SELECT DISTINCT ProcessId,
                        BacklogCategoryReferenceName,
                        BacklogName,
                        BacklogLevel,
                        BacklogType
                FROM    #Backlogs
            ) bl
    ON      bb.ProcessId = bl.ProcessId
            AND bb.BugsBacklog = bl.BacklogCategoryReferenceName
    WHERE   src.IsBugType = 1
            AND src.HasBacklog = 0

    ;WITH Tgt AS
    (
        SELECT  *
        FROM    AnalyticsModel.tbl_Process
        WHERE   PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) Tgt AS t
    USING #Src AS s
    ON  (
            t.ProjectSK = s.ProjectSK
            AND t.TeamSK = s.TeamSK
            AND t.WorkItemType = s.WorkItemType
        )
    WHEN MATCHED AND NOT EXISTS
    (
        SELECT  s.ProjectSK,
                s.WorkItemType,
                s.TeamSK,
                s.IsHiddenType,
                s.IsBugType,
                s.HasBacklog,
                s.BacklogCategoryReferencename,
                s.BacklogName,
                s.BacklogType,
                s.BacklogLevel
        INTERSECT
        SELECT  t.ProjectSK,
                t.WorkItemType,
                t.TeamSK,
                t.IsHiddenType,
                t.IsBugType,
                t.HasBacklog,
                t.BacklogCategoryReferencename,
                t.BacklogName,
                t.BacklogType,
                t.BacklogLevel
    )
    THEN
    UPDATE SET AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            IsHiddenType = s.IsHiddenType,
            IsBugType = s.IsBugType,
            HasBacklog = s.HasBacklog,
            BacklogCategoryReferenceName = s.BacklogCategoryReferencename,
            BacklogName = s.BacklogName,
            BacklogType = s.BacklogType,
            BacklogLevel = s.BacklogLevel
    WHEN NOT MATCHED BY TARGET THEN
    INSERT  (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            ProjectSK,
            WorkItemType,
            TeamSK,
            IsHiddenType,
            IsBugType,
            HasBacklog,
            BacklogCategoryReferencename,
            BacklogName,
            BacklogType,
            BacklogLevel
    )
    VALUES  (
            @PartitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.ProjectSK,
            s.WorkItemType,
            s.TeamSK,
            s.IsHiddenType,
            s.IsBugType,
            s.HasBacklog,
            s.BacklogCategoryReferencename,
            s.BacklogName,
            s.BacklogType,
            s.BacklogLevel
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #TeamProcesses
    DROP TABLE #Src
    DROP TABLE #WorkItemTypes
    DROP TABLE #Backlogs

    RETURN 0
END

GO

