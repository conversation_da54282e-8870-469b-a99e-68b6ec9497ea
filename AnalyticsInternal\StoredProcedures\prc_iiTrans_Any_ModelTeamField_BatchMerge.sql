/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 930DB50443E13B8F585D64CD1A5C68CBBA8824D9
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelTeamField_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 50000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #Src
    (
        ProjectId               UNIQUEIDENTIFIER    NOT NULL,
        TeamFieldReferenceName  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL,
        TeamFieldValue          NVARCHAR(4000)      COLLATE DATABASE_DEFAULT NOT NULL, -- will be null when AreaId is the key
        AreaId                  UNIQUEIDENTIFIER    NULL, -- will be null when TeamFieldValue is the key
    )

    DECLARE @areaTriggerBatchIdStart BIGINT = 0, @areaTriggerBatchIdEnd BIGINT = 9223372036854775807
    IF (@triggerTableName = 'WorkItemArea')
    BEGIN
        SET @areaTriggerBatchIdStart = @triggerBatchIdStart
        SET @areaTriggerBatchIdEnd = @triggerBatchIdEnd
    END

    -- insert area mapped fields
    -- based on process field type config
    INSERT  #Src
    SELECT  pj.ProjectGuid,
            ISNULL(ptf.ReferenceName, 'System.AreaPath') AS ReferenceName,
            a.AreaPath,
            a.AreaId
    FROM    AnalyticsStage.tbl_Project pj
    JOIN    AnalyticsStage.tbl_Process p
    ON      p.PartitionId = pj.PartitionId
            AND p.ProcessId = pj.ProcessId
    OUTER APPLY
        (
        SELECT  tf.Item.value('ReferenceName[1]','NVARCHAR(256)') AS ReferenceName
        FROM    p.TypeFields.nodes('//Item') AS tf(Item)
        WHERE   tf.Item.value('Type[1]','NVARCHAR(70)') = 'Team'
        ) ptf
    JOIN    AnalyticsModel.tbl_Area a
    ON      a.PartitionId = pj.PartitionId
            AND a.ProjectSK = pj.ProjectGuid
            AND a.AnalyticsBatchId BETWEEN @areaTriggerBatchIdStart AND @areaTriggerBatchIdEnd
    WHERE   pj.PartitionId = @partitionId
            AND ISNULL(ptf.ReferenceName, 'System.AreaPath') = 'System.AreaPath'
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@triggerTableName = 'TeamSetting')
    BEGIN
        -- insert other values found in TeamSetting
        INSERT  #Src
        SELECT  DISTINCT ProjectGuid,
                TeamFieldReferenceName,
                TeamFieldValue,
                NULL AS AreaId
        FROM
            (
            SELECT  t.ProjectGuid,
                    t.TeamFieldReferenceName,
                    tf.Item.value('Value[1]','NVARCHAR(4000)') AS TeamFieldValue
            FROM    AnalyticsStage.tbl_TeamSetting t
            CROSS APPLY TeamFieldValues.nodes('//Item') AS tf(Item)
            WHERE t.PartitionId = @partitionId
                AND t.TeamFieldReferenceName <> 'System.AreaPath'
            ) ts
        WHERE   TeamFieldValue IS NOT NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    UPDATE  TOP (@batchSizeMax) t
    SET     t.TeamFieldValue = s.TeamFieldValue
    FROM    AnalyticsModel.tbl_TeamField t
    JOIN    #Src s
    ON      t.ProjectId = s.ProjectId
            AND t.TeamFieldReferenceName = s.TeamFieldReferenceName
            AND (t.AreaId = s.AreaId OR (t.AreaId IS NULL AND s.AreaId IS NULL))
            AND IIF(t.TeamFieldReferenceName = 'System.AreaPath', '', t.TeamFieldValue) = IIF(s.TeamFieldReferenceName = 'System.AreaPath', '', s.TeamFieldValue)
    WHERE   t.PartitionId = @partitionId
            AND NOT EXISTS (SELECT t.TeamFieldValue INTERSECT SELECT s.TeamFieldValue)

    SET @updatedCount = @@ROWCOUNT
    SET @complete = CASE WHEN @updatedCount < @batchSizeMax THEN 1 ELSE 0 END

    IF (@complete = 1)
    BEGIN
        DELETE  s
        FROM    AnalyticsModel.tbl_TeamField t
        JOIN    #Src s
        ON      t.ProjectId = s.ProjectId
                AND t.TeamFieldReferenceName = s.TeamFieldReferenceName
                AND (t.AreaId = s.AreaId OR (t.AreaId IS NULL AND s.AreaId IS NULL))
                AND IIF(t.TeamFieldReferenceName = 'System.AreaPath', '', t.TeamFieldValue) = IIF(s.TeamFieldReferenceName = 'System.AreaPath', '', s.TeamFieldValue)
        WHERE   t.PartitionId = @partitionId

        INSERT  TOP (@batchSizeMax) AnalyticsModel.tbl_TeamField
                (
                PartitionId,
                AnalyticsBatchId,
                AnalyticsCreatedDate,
                AnalyticsUpdatedDate,
                ProjectId,
                TeamFieldReferenceName,
                AreaId,
                TeamFieldValue
                )
        SELECT  @partitionId,
                @batchId,
                @batchDt,
                @batchDt,
                s.ProjectId,
                s.TeamFieldReferenceName,
                s.AreaId,
                s.TeamFieldValue
        FROM    #Src s

        SET @insertedCount = @@ROWCOUNT
        SET @complete = CASE WHEN @insertedCount < @batchSizeMax THEN 1 ELSE 0 END
    END

    DROP TABLE #Src

    RETURN 0
END

GO

