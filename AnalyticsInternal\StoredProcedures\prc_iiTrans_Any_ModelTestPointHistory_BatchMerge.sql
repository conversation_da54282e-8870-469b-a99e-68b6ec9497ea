/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5FEDB6D39CA302FA04719D24A545B5B0DD1FA2B0
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelTestPointHistory_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ChangedTestPointHistory
    (
        TestPointId         INT               NOT NULL,
        Revision            INT               NOT NULL,
        TestPointRevision   INT               NOT NULL,   -- This is the revision that model test point table has
        TestPointSK         INT               NOT NULL,
        TestConfigurationId INT               NOT NULL,
        TestConfigurationSK INT               NOT NULL,
        ChangedDate         DATETIME          NOT NULL,
        IsDeleted           BIT               NOT NULL,
        DataSourceId        INT               NOT NULL,
        AssignedTo          UNIQUEIDENTIFIER  NULL,
        Priority            INT               NULL,
        AutomationStatus    NVARCHAR(256)     COLLATE DATABASE_DEFAULT NULL
    )

    IF (@triggerTableName = 'Model.TestPoint')
    BEGIN
        IF (@subBatchCount <= 1)
        BEGIN
            -- Initial batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
            -- don't differ much. Go with IX_tbl_TestPoint_AnalyticsBatchId
            INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
            SELECT   TOP (@batchSizeMax) WITH TIES
                     ph.TestPointId,
                     ph.Revision,
                     mtp.Revision,
                     mtp.TestPointSK,
                     mtc.TestConfigurationId,
                     mtc.TestConfigurationSK,
                     ph.ChangedDate,
                     (ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                     ph.DataSourceId,
                     mtp.AssignedToUserSK,
                     mtp.Priority,
                     mtp.AutomationStatus
            FROM     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_AnalyticsBatchId))
            JOIN     AnalyticsStage.tbl_TestPointHistory ph WITH (INDEX (CI_tbl_TestPointHistory))
            ON       ph.PartitionId     = @partitionId
                     AND ph.TestPointId = mtp.TestPointId
            JOIN     AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
            ON       mtc.PartitionId             = @partitionId
                     AND mtc.TestConfigurationId = ph.TestConfigurationId
            WHERE    mtp.PartitionId = @partitionId
                     AND mtp.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                     AND mtp.TestPointId > ISNULL(@stateData, -1)
                     AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
            ORDER BY mtp.TestPointId
            OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
        END
        ELSE
        BEGIN
            -- Subsequent batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
            -- cover a lot of batches in between. Go with IX_tbl_TestPoint_TestPointId to avoid expensive sorts
            INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
            SELECT   TOP (@batchSizeMax) WITH TIES
                     ph.TestPointId,
                     ph.Revision,
                     mtp.Revision,
                     mtp.TestPointSK,
                     mtc.TestConfigurationId,
                     mtc.TestConfigurationSK,
                     ph.ChangedDate,
                     (ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                     ph.DataSourceId,
                     mtp.AssignedToUserSK,
                     mtp.Priority,
                     mtp.AutomationStatus
            FROM     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_TestPointId))
            JOIN     AnalyticsStage.tbl_TestPointHistory ph WITH (INDEX (CI_tbl_TestPointHistory))
            ON       ph.PartitionId     = @partitionId
                     AND ph.TestPointId = mtp.TestPointId
            JOIN     AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
            ON       mtc.PartitionId             = @partitionId
                     AND mtc.TestConfigurationId = ph.TestConfigurationId
            WHERE    mtp.PartitionId = @partitionId
                     AND mtp.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                     AND mtp.TestPointId > ISNULL(@stateData, -1)
                     AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
            ORDER BY mtp.TestPointId
            OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
        END
    END
    ELSE IF (@triggerTableName = 'Model.TestConfiguration')
    BEGIN
        INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
        SELECT   TOP (@batchSizeMax) WITH TIES
                 ph.TestPointId,
                 ph.Revision,
                 mtp.Revision,
                 mtp.TestPointSK,
                 mtc.TestConfigurationId,
                 mtc.TestConfigurationSK,
                 ph.ChangedDate,
                 (ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                 ph.DataSourceId,
                 mtp.AssignedToUserSK,
                 mtp.Priority,
                 mtp.AutomationStatus
        FROM     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_TestPointId))
        INNER LOOP JOIN AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
        ON       mtc.PartitionId = @partitionId
                 AND mtc.TestConfigurationId = mtp.TestConfigurationId
        JOIN     AnalyticsStage.tbl_TestPointHistory ph WITH (INDEX (CI_tbl_TestPointHistory))
        ON       ph.PartitionId     = @partitionId
                 AND ph.TestPointId = mtp.TestPointId
        WHERE    mtp.PartitionId = @partitionId
                 AND mtc.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                 AND mtp.TestPointId > ISNULL(@stateData, -1)
                 AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
        ORDER BY mtp.TestPointId
        OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
    END
    ELSE IF (@triggerTableName = 'Model.TestSuite')
    BEGIN
        INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
        SELECT   TOP (@batchSizeMax) WITH TIES
                 ph.TestPointId,
                 ph.Revision,
                 mtp.Revision,
                 mtp.TestPointSK,
                 mtc.TestConfigurationId,
                 mtc.TestConfigurationSK,
                 ph.ChangedDate,
                 (~ISNULL(ph.Enabled, 1) | ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                 ph.DataSourceId,
                 mtp.AssignedToUserSK,
                 mtp.Priority,
                 mtp.AutomationStatus
        FROM     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_TestPointId))
        INNER LOOP JOIN AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_TestPlanId_TestSuiteId))
        ON       mts.PartitionId = @partitionId
                 AND mts.TestPlanId = mtp.TestPlanId
                 AND mts.TestSuiteId = mtp.TestSuiteId
        JOIN     AnalyticsStage.tbl_TestPointHistory ph WITH (INDEX (CI_tbl_TestPointHistory))
        ON       ph.PartitionId     = @partitionId
                 AND ph.TestPointId = mtp.TestPointId
        JOIN     AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
        ON       mtc.PartitionId             = @partitionId
                 AND mtc.TestConfigurationId = mtp.TestConfigurationId
        WHERE    mtp.PartitionId = @partitionId
                 AND mts.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                 AND mtp.TestPointId > ISNULL(@stateData, -1)
                 AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
        ORDER BY mtp.TestPointId
        OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
    END
    ELSE IF (@triggerTableName = 'TestPointHistory')
    BEGIN
        IF (@subBatchCount <= 1)
        BEGIN
            -- Initial batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
            -- don't differ much. Go with IX_tbl_TestPointHistory_AxBatchIdChanged
            INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
            SELECT   TOP (@batchSizeMax) WITH TIES
                     ph.TestPointId,
                     ph.Revision,
                     mtp.Revision,
                     mtp.TestPointSK,
                     mtc.TestConfigurationId,
                     mtc.TestConfigurationSK,
                     ph.ChangedDate,
                     (ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                     ph.DataSourceId,
                     mtp.AssignedToUserSK,
                     mtp.Priority,
                     mtp.AutomationStatus
            FROM     AnalyticsStage.tbl_TestPointHistory ph WITh (INDEX (IX_tbl_TestPointHistory_AxBatchIdChanged))
            JOIN     AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
            ON       mtc.PartitionId             = @partitionId
                     AND mtc.TestConfigurationId = ph.TestConfigurationId
            JOIN     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_TestPointId))
            ON       mtp.PartitionId               = @partitionId
                     AND mtp.TestPointId           = ph.TestPointId
                     AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
            WHERE    ph.PartitionId      = @partitionId
                     AND ph.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                     AND ph.TestPointId > ISNULL(@stateData, -1)
            ORDER BY ph.TestPointId
            OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
        END
        ELSE
        BEGIN
            -- Subsequent batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
            -- cover a lot of batches in between. Go with CI_tbl_TestPointHistory to avoid expensive sorts
            INSERT   #ChangedTestPointHistory (TestPointId, Revision, TestPointRevision, TestPointSK, TestConfigurationId, TestConfigurationSK, ChangedDate, IsDeleted, DataSourceId, AssignedTo, Priority, AutomationStatus)
            SELECT   TOP (@batchSizeMax) WITH TIES
                     ph.TestPointId,
                     ph.Revision,
                     mtp.Revision,
                     mtp.TestPointSK,
                     mtc.TestConfigurationId,
                     mtc.TestConfigurationSK,
                     ph.ChangedDate,
                     (ph.IsDeleted | mtp.IsDeleted), -- Set IsDeleted for point history to 1 for deleted test points
                     ph.DataSourceId,
                     mtp.AssignedToUserSK,
                     mtp.Priority,
                     mtp.AutomationStatus
            FROM     AnalyticsStage.tbl_TestPointHistory ph WITh (INDEX (CI_tbl_TestPointHistory))
            JOIN     AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId))
            ON       mtc.PartitionId             = @partitionId
                     AND mtc.TestConfigurationId = ph.TestConfigurationId
            JOIN     AnalyticsModel.tbl_TestPoint mtp WITH (INDEX (IX_tbl_TestPoint_TestPointId))
            ON       mtp.PartitionId               = @partitionId
                     AND mtp.TestPointId           = ph.TestPointId
                     AND mtp.HasWorkItemProperties = 1 -- Avoid test points for which work item information isn't yet arrived
            WHERE    ph.PartitionId      = @partitionId
                     AND ph.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                     AND ph.TestPointId > ISNULL(@stateData, -1)
            ORDER BY ph.TestPointId
            OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPointHistory)
        END
    END

    CREATE TABLE #TestPointHistory
    (
        PartitionId                 INT               NOT NULL,
        ProjectSK                   UNIQUEIDENTIFIER  NULL,
        TestSuiteSK                 INT               NULL,
        TestPointSK                 INT               NULL,
        TestPlanId                  INT               NULL,
        TestSuiteId                 INT               NULL,
        TestPointId                 INT               NOT NULL,
        TestConfigurationSK         INT               NULL,
        TestConfigurationId         INT               NULL,
        TestCaseId                  INT               NULL,
        Enabled                     BIT               NULL,
        State                       TINYINT           NULL,
        TesterSK                    UNIQUEIDENTIFIER  NULL,
        AssignedToSK                UNIQUEIDENTIFIER  NULL,
        Priority                    INT               NULL,
        AutomationStatus            NVARCHAR(256)     COLLATE DATABASE_DEFAULT NULL,
        ResultOutcome               TINYINT           NULL,
        ResultFromDate              DATETIMEOFFSET(0) NULL,
        ResultToDate                DATETIMEOFFSET(0) NULL,
        IsLastRevisionOfDay         BIT               NULL,
        IsDeleted                   BIT               NOT NULL,
        DatasourceId                INT               NOT NULL,
        Revision                    INT               NOT NULL
    )

    -- Self join with TestPointHistory table and insert the records of n-1 th revision when processing nth revision of point,
    -- that way we don't insert the latest revision in TestPointHistory model table
    INSERT  #TestPointHistory
    (
        PartitionId,
        ProjectSK,
        TestSuiteSK,
        TestPlanId,
        TestSuiteId,
        TestPointSK,
        TestPointId,
        TestConfigurationSK,
        TestConfigurationId,
        TestCaseId,
        Enabled,
        State,
        TesterSK,
        AssignedToSK,
        Priority,
        AutomationStatus,
        ResultOutcome,
        ResultFromDate,
        ResultToDate,
        IsDeleted,
        DataSourceId,
        Revision
    )
    SELECT  ph.PartitionId,
            mts.ProjectSK,
            mts.TestSuiteSK, -- We take TestSuiteId and TestSuiteSK from tbl_TestPointHistory of (n-1)th revisions as a point can be moved to different suites over its lifetime
            ph.TestPlanId,
            ph.TestSuiteId,
            cph.TestPointSK, -- We take TestPointSK from #ChangedTestPointHistory because it can't change across revisions
            ph.TestPointId,
            cph.TestConfigurationSK, -- We take TestConfigurationSK from #ChangedTestPointHistory because for a given TestPointId, it can't change.
            ph.TestConfigurationId,
            ph.TestCaseId,
            ph.Enabled,
            ph.State,
            AnalyticsInternal.func_GetUserSKFromWITPerson(ph.Tester),
            cph.AssignedTo,
            cph.Priority,
            cph.AutomationStatus,
            IIF (ph.TestResultOutcome IS NULL, 1, IIF(ph.TestResultOutcome < 2, 1, ph.TestResultOutcome)), -- Set TestResultOutcome to 1 when it is NULL or 'Unspecified' so that we get a single value for active points
            ph.ChangedDate AT TIME ZONE @timeZone, -- ph.ChangedDate represents the ChangedDate for (n-1)th revisions
            cph.ChangedDate AT TIME ZONE @timeZone, -- cph.ChangedDate represents the ChangedDate for nth revisions
            cph.IsDeleted | ISNULL(mts.IsDeleted, 0) | IIF (mts.TestPlanState IS NULL, 0, IIF(mts.TestPlanState = 255, 1, 0)), -- Set IsDeleted for point history for deleted test plans / test suites too
            ph.DataSourceId,
            ph.Revision
    FROM    #ChangedTestPointHistory cph
    JOIN    AnalyticsStage.tbl_TestPointHistory ph WITH (INDEX (CI_tbl_TestPointHistory))
    ON      ph.PartitionId      = @partitionId
            AND ph.TestPointId  = cph.TestPointId
            AND ph.Revision     = cph.Revision - 1

            AND ph.DataSourceId = cph.DataSourceId
    JOIN    AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_TestPlanId_TestSuiteId))
    ON      mts.PartitionId     = @partitionId
            AND mts.TestPlanId  = ph.TestPlanId
            AND mts.TestSuiteId = ph.TestSuiteId
    WHERE   ph.Revision < cph.TestPointRevision
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- In most cases, this would only result in inserts
    ;WITH Tgt AS (
        SELECT * FROM AnalyticsModel.tbl_TestPointHistory
        WHERE PartitionId = @partitionId
    )
    MERGE Tgt t
    USING #TestPointHistory s
    ON (t.PartitionId = @partitionId AND t.TestPointId = s.TestPointId AND t.Revision = s.Revision AND t.DataSourceId = s.DataSourceId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.State,
        s.TesterSK,
        s.AssignedToSK,
        s.Priority,
        s.AutomationStatus,
        s.ResultOutcome,
        s.ResultFromDate,
        s.ResultToDate,
        s.IsDeleted
        INTERSECT
        SELECT
        t.State,
        t.TesterUserSK,
        t.AssignedToUserSK,
        t.Priority,
        t.AutomationStatus,
        t.ResultOutcome,
        t.ResultFromDate,
        t.ResultToDate,
        t.IsDeleted
    ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId     = @batchId,
        State                = s.State,
        TesterUserSK         = s.TesterSK,
        AssignedToUserSK     = s.AssignedToSK,
        Priority             = s.Priority,
        AutomationStatus     = s.AutomationStatus,
        ResultOutcome        = s.ResultOutcome,
        ResultFromDate       = s.ResultFromDate,
        ResultFromDateSK     = AnalyticsInternal.func_GenDateSK(s.ResultFromDate),
        ResultToDate         = s.ResultToDate,
        ResultToDateSK       = AnalyticsInternal.func_GenDateSK(s.ResultToDate),
        IsLastRevisionOfDay  = IIF (AnalyticsInternal.func_GenDateSK(s.ResultToDate) > AnalyticsInternal.func_GenDateSK(s.ResultFromDate), 1, 0),
        IsDeleted            = s.IsDeleted
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ProjectSK,
        TestSuiteSK,
        TestPlanId,
        TestSuiteId,
        TestPointSK,
        TestPointId,
        TestConfigurationSK,
        TestConfigurationId,
        TestCaseId,
        Revision,
        State,
        TesterUserSK,
        AssignedToUserSK,
        Priority,
        AutomationStatus,
        ResultOutcome,
        ResultFromDate,
        ResultFromDateSK,
        ResultToDate,
        ResultToDateSK,
        InternalForSnapshotHashJoin,
        IsLastRevisionOfDay,
        IsDeleted
    )
    VALUES (
        @partitionId,
        @batchDt,
        @batchDt,
        @batchId,
        s.ProjectSK,
        s.TestSuiteSK,
        s.TestPlanId,
        s.TestSuiteId,
        s.TestPointSK,
        s.TestPointId,
        s.TestConfigurationSK,
        s.TestConfigurationId,
        s.TestCaseId,
        s.Revision,
        s.State,
        s.TesterSK,
        s.AssignedToSK,
        s.Priority,
        s.AutomationStatus,
        s.ResultOutcome,
        s.ResultFromDate,
        AnalyticsInternal.func_GenDateSK(s.ResultFromDate),
        s.ResultToDate,
        AnalyticsInternal.func_GenDateSK(s.ResultToDate),
        1,
        IIF (AnalyticsInternal.func_GenDateSK(s.ResultToDate) > AnalyticsInternal.func_GenDateSK(s.ResultFromDate), 1, 0),
        s.IsDeleted
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestPointHistory
    DROP TABLE #ChangedTestPointHistory

    RETURN 0
END

GO

