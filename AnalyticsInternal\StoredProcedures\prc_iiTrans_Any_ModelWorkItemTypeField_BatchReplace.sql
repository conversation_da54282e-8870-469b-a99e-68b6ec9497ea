/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A49C64A9642392C4884332675CEC345AA9DA7A86
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelWorkItemTypeField_BatchReplace
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    DECLARE @changes TABLE
    (
        MergeAction     NVARCHAR(10)
    );

    --A join between work item types and work item type categories
    CREATE TABLE #WorkItemType
    (
        ProcessId                UNIQUEIDENTIFIER,
        WorkItemTypeCategory     NVARCHAR(70)      COLLATE DATABASE_DEFAULT,
        WorkItemType             NVARCHAR(128)     COLLATE DATABASE_DEFAULT,
        INDEX CI_WorkItemType CLUSTERED (ProcessId, WorkItemType)
    );

    CREATE TABLE #FieldType
    (
        ProcessId          UNIQUEIDENTIFIER,
        FieldName          NVARCHAR(128)     COLLATE DATABASE_DEFAULT,
        FieldType          NVARCHAR(50)      COLLATE DATABASE_DEFAULT,
        WorkItemType       NVARCHAR(128)     COLLATE DATABASE_DEFAULT,
        FieldReferenceName NVARCHAR(386)     COLLATE DATABASE_DEFAULT,
        INDEX CI_FieldType CLUSTERED (ProcessId, FieldName)
    );

    -- Parse work item type categories from the process table.
    -- 54827A90-29A2-4F45-9FB2-282302C71095    2    Bug Category    <MemberWorkItemTypeNames>Bug</MemberWorkItemTypeNames>
    ;WITH UnprocessedWorkItemType AS (
        SELECT  p.ProcessId                                      AS ProcessId,
                TypeCategory.x.value('Name[1]','nvarchar(70)')   AS WorkItemTypeCategory,
                TypeCategory.x.query('MemberWorkItemTypeNames')  AS WorkItemTypeNames
        FROM    AnalyticsStage.tbl_Process p
        CROSS APPLY TypeCategories.nodes('//Item') AS TypeCategory(x)
        WHERE   PartitionId = @partitionId
    )
    INSERT  #WorkItemType (ProcessId, WorkItemTypeCategory, WorkItemType)
    SELECT  UnprocessedWorkItemType.ProcessId,
            UnprocessedWorkItemType.WorkItemTypeCategory,
            workItemTypeName.x.value('(text())[1]', 'nvarchar(128)')   AS WorkItemType
    FROM    UnprocessedWorkItemType
    CROSS APPLY WorkItemTypeNames.nodes('MemberWorkItemTypeNames') AS workItemTypeName(x)
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1)

    -- Parse field names from the process table.
    -- 54827A90-29A2-4F45-9FB2-282302C71095    2    Iteration Path    TreePath    <WorkItemTypes>Bug</WorkItemTypes><WorkItemTypes>Epic</WorkItemTypes><WorkItemTypes>Feature</WorkItemTypes><WorkItemTypes>Task</WorkItemTypes><WorkItemTypes>Issue</WorkItemTypes><WorkItemTypes>Change Request</WorkItemTypes><WorkItemTypes>Risk</WorkItemTypes><WorkItemTypes>Requirement</WorkItemTypes><WorkItemTypes>Review</WorkItemTypes><WorkItemTypes>Test Case</WorkItemTypes>
    ;WITH UnprocessedField AS (
        SELECT  p.ProcessId,
                Field.x.value('Name[1]','nvarchar(128)')          AS FieldName,
                Field.x.value('ReferenceName[1]','nvarchar(386)') AS FieldReferenceName,
                Field.x.value('Type[1]','nvarchar(50)')           AS FieldType,
                Field.x.query('WorkItemTypes')                    AS WorkItemTypeNames
        FROM    AnalyticsStage.tbl_Process p
        CROSS APPLY Fields.nodes('//Item') AS Field(x)
        WHERE   PartitionId = @partitionId
    )
    INSERT  #FieldType (ProcessId, FieldName, FieldType, WorkItemType, FieldReferenceName)
    SELECT  UnprocessedField.ProcessId,
            UnprocessedField.FieldName,
            UnprocessedField.FieldType,
            workItemTypeName.x.value('(text())[1]', 'nvarchar(128)') AS WorkItemType,
            UnprocessedField.FieldReferenceName
    FROM    UnprocessedField
    CROSS APPLY WorkItemTypeNames.nodes('WorkItemTypes') AS workItemTypeName(x)
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1)

    CREATE TABLE #WorkItemTypeField
    (
        ProjectSK                   UNIQUEIDENTIFIER,
        FieldName                   NVARCHAR(128)     COLLATE DATABASE_DEFAULT,
        FieldType                   NVARCHAR(50)      COLLATE DATABASE_DEFAULT,
        WorkItemTypeCategory        NVARCHAR(70)      COLLATE DATABASE_DEFAULT,
        WorkItemType                NVARCHAR(128)     COLLATE DATABASE_DEFAULT,
        FieldReferenceName          NVARCHAR(386)     COLLATE DATABASE_DEFAULT,
        INDEX CI_#WorkItemTypeField UNIQUE CLUSTERED (ProjectSK, FieldName, WorkItemType, WorkItemTypeCategory)
    )

    -- Join fields and work item type categories on the work item type names,
    -- Join to project on process id
    INSERT  #WorkItemTypeField (ProjectSK, FieldName, FieldType, WorkItemTypeCategory, WorkItemType, FieldReferenceName)
    SELECT  p.ProjectGuid,
            f.FieldName,
            f.FieldType,
            ISNULL(w.WorkItemTypeCategory, ''),
            f.WorkItemType,
            f.FieldReferenceName
    FROM    #FieldType f
    JOIN    AnalyticsStage.tbl_Project p
    ON      p.PartitionId = @partitionId
            AND p.ProcessId = f.ProcessId
    LEFT JOIN #WorkItemType w ON w.ProcessId = f.ProcessId
            AND w.WorkItemType = f.WorkItemType
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- MERGE DATA INTO TARGET TABLE

    -- clean up where project no longer exists in stage
    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_WorkItemTypeField t
    WHERE   t.PartitionId = @partitionId
            AND t.ProjectSK NOT IN (SELECT ProjectGuid FROM AnalyticsStage.tbl_Project WHERE PartitionId = @partitionId)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    -- clean up as long as project has any fields - avoids cleaning up in response to process staging errors
    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_WorkItemTypeField t
    LEFT HASH JOIN #WorkItemTypeField s
    ON      t.ProjectSK = s.ProjectSK
            AND t.FieldName = s.FieldName
            AND t.WorkItemType = s.WorkItemType
            AND t.WorkItemTypeCategory = s.WorkItemTypeCategory
    WHERE   t.PartitionId = @partitionId
            AND s.ProjectSK IS NULL
            AND t.ProjectSK IN (SELECT ProjectSK FROM #WorkItemTypeField)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount += @@ROWCOUNT

    UPDATE  TOP (@batchSizeMax) t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            FieldType = s.FieldType,
            FieldReferenceName = s.FieldReferenceName
    FROM    AnalyticsModel.tbl_WorkItemTypeField t
    INNER HASH JOIN #WorkItemTypeField s
    ON      t.PartitionId = @partitionId
            AND t.ProjectSK = s.ProjectSK
            AND t.FieldName = s.FieldName
            AND t.WorkItemType = s.WorkItemType
            AND t.WorkItemTypeCategory = s.WorkItemTypeCategory
    WHERE   NOT EXISTS (
            SELECT  s.FieldType,
                    s.FieldReferenceName
            INTERSECT
            SELECT  t.FieldType,
                    t.FieldReferenceName
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    -- remove those rows already in target table
    DELETE  s
    FROM    AnalyticsModel.tbl_WorkItemTypeField t
    INNER HASH JOIN #WorkItemTypeField s
    ON      t.PartitionId = @partitionId
            AND t.ProjectSK = s.ProjectSK
            AND t.FieldName = s.FieldName
            AND t.WorkItemType = s.WorkItemType
            AND t.WorkItemTypeCategory = s.WorkItemTypeCategory
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  AnalyticsModel.tbl_WorkItemTypeField
        (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        ProjectSK,
        FieldName,
        FieldType,
        WorkItemTypeCategory,
        WorkItemType,
        FieldReferenceName
        )
    SELECT  TOP (@batchSizeMax) @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.ProjectSK,
            s.FieldName,
            s.FieldType,
            s.WorkItemTypeCategory,
            s.WorkItemType,
            s.FieldReferenceName
    FROM    #WorkItemTypeField s
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax AND @updatedCount < @batchSizeMax AND @insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #WorkItemTypeField
    DROP TABLE #WorkItemType
    DROP TABLE #FieldType

    RETURN 0
END

GO

