/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B67AD263BC2FAA49E0492E0BE2CCD36C7436C6E2
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Daily_ModelTest_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    --Get the retention date
    DECLARE @retentionDays INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'Target.RetentionDays'), 30)
    DECLARE @retentionDate DATE = CAST(DATEADD(day, 0 - @retentionDays, GETUTCDATE() AT TIME ZONE @timeZone) AS DATE)

    CREATE TABLE #DeletedTest (TestSK INT)
    CREATE TABLE #InUse (TestSK INT)

    INSERT  #InUse
    SELECT  DISTINCT TestSK
    FROM    AnalyticsModel.tbl_TestResult
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #InUse
    SELECT  DISTINCT TestSK
    FROM    AnalyticsModel.tbl_TestResultDaily
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- using temp tables to hold hashed TestSK from large CCI faser than doing it in a hash join to CCI
    INSERT  #DeletedTest
    SELECT  TOP (@batchSizeMax) d.TestSK
    FROM    AnalyticsModel.tbl_Test d
    WHERE   d.PartitionId = @partitionId
            AND d.AnalyticsCreatedDate < @retentionDate
            AND NOT EXISTS (SELECT * FROM #InUse WHERE TestSK = d.TestSK)
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), HASH JOIN)

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)

    DELETE  t
    FROM    AnalyticsModel.tbl_Test t WITH (INDEX (CL_AnalyticsModel_tbl_Test)) -- force use of CCI
    JOIN    #DeletedTest d
    ON      d.TestSK = t.TestSK
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedTest
    DROP TABLE #InUse

    RETURN 0
END

GO

