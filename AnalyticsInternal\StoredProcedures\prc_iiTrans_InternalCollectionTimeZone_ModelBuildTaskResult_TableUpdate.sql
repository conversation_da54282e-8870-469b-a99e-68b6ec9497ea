/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: C9BE54526A6CC5184A3948E033E69BC8A6D1760D
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelBuildTaskResult_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @planIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @planIdEnd BIGINT
    DECLARE @planIdCount BIGINT

    SELECT @planIdEnd = MAX(PlanId), @planIdCount = COUNT(*)
    FROM
    (
        SELECT  TOP (@batchSizeMax) PlanId
        FROM    AnalyticsModel.tbl_BuildTaskResult
        WHERE   PartitionId = @partitionId
                AND PlanId >= @planIdStart
        ORDER   BY PlanId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @planIdEnd
    SET @complete = IIF(@planIdCount < @batchSizeMax, 1, 0)

    UPDATE  t
    SET     t.AnalyticsUpdatedDate = @batchDt,
            t.AnalyticsBatchId = @batchId,
            t.ActivityStartedDate = t.ActivityStartedDate AT TIME ZONE @timeZone,
            t.ActivityStartedDateSK = AnalyticsInternal.func_GenDateSK(t.ActivityStartedDate AT TIME ZONE @timeZone),
            t.ActivityCompletedDate = t.ActivityCompletedDate AT TIME ZONE @timeZone,
            t.ActivityCompletedDateSK = AnalyticsInternal.func_GenDateSK(t.ActivityCompletedDate AT TIME ZONE @timeZone),
            t.BuildQueuedDateSK = AnalyticsInternal.func_GenDateSK(b.QueuedDate AT TIME ZONE @timeZone),
            t.BuildStartedDateSK = AnalyticsInternal.func_GenDateSK(b.StartedDate AT TIME ZONE @timeZone),
            t.BuildCompletedDateSK = AnalyticsInternal.func_GenDateSK(b.CompletedDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_BuildTaskResult t
    JOIN    AnalyticsModel.tbl_Build b
    ON      t.PartitionId = b.PartitionId
            AND t.BuildId = b.BuildId
    WHERE   t.PartitionId = @partitionId
            AND PlanId BETWEEN @planIdStart AND @planIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(t.ActivityStartedDate AS DATETIME),
                CAST(t.ActivityCompletedDate AS DATETIME),
                t.BuildQueuedDateSK,
                t.BuildStartedDateSK,
                t.BuildCompletedDateSK
                INTERSECT
                SELECT
                CAST(t.ActivityStartedDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(t.ActivityCompletedDate AT TIME ZONE @timeZone AS DATETIME),
                AnalyticsInternal.func_GenDateSK(b.QueuedDate AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(b.StartedDate AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(b.CompletedDate AT TIME ZONE @timeZone)
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

