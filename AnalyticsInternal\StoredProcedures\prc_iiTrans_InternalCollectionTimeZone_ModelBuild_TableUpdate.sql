/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E458F0C357C78A3FA195A21F6999CF22FEB54E32
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelBuild_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @buildIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @buildIdEnd BIGINT
    DECLARE @buildCount BIGINT

    SELECT @buildIdEnd = MAX(BuildId), @buildCount = COUNT(*)
    FROM
    (
        SELECT  TOP (@batchSizeMax) BuildId
        FROM    AnalyticsModel.tbl_Build
        WHERE   PartitionId = @partitionId
                AND BuildId >= @buildIdStart
        ORDER   BY BuildId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @buildIdEnd
    SET @complete = IIF(@buildCount < @batchSizeMax, 1, 0)

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            QueuedDate = QueuedDate AT TIME ZONE @timeZone,
            QueuedDateSK = AnalyticsInternal.func_GenDateSK(QueuedDate AT TIME ZONE @timeZone),
            StartedDate = StartedDate AT TIME ZONE @timeZone,
            StartedDateSK = AnalyticsInternal.func_GenDateSK(StartedDate AT TIME ZONE @timeZone),
            CompletedDate = CompletedDate AT TIME ZONE @timeZone,
            CompletedDateSK = AnalyticsInternal.func_GenDateSK(CompletedDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_Build t
    WHERE   PartitionId = @partitionId
            AND BuildId BETWEEN @buildIdStart AND @buildIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(QueuedDate AS DATETIME),
                CAST(StartedDate AS DATETIME),
                CAST(CompletedDate AS DATETIME)
                INTERSECT
                SELECT
                CAST(QueuedDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(StartedDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(CompletedDate AT TIME ZONE @timeZone AS DATETIME)
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

