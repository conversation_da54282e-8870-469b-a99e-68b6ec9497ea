/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 1FC90DA220F2C8FBC14ABB56EC1A8E91079C7E9F
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelIteration_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    ;WITH Iteration AS
    (
        SELECT  *,
                CAST(i.StartDate AS DATETIME) AT TIME ZONE @timeZone AS NewStartDate,
                -- DateTimeOffset/DateTime2 has 100ns accuracy, but Datetime Accuracy Rounded to increments of .000, .003, or .007 seconds
                -- Using Datetime2 in coversions
                IIF(CAST(i.EndDate AS DATE) < '9999-01-01', DATEADD(ms, 86399999, CAST(CAST(i.EndDate AS DATE) AS DATETIME2)), CAST(i.EndDate AS DATETIME2)) AT TIME ZONE @timeZone AS NewEndDate
        FROM    AnalyticsModel.tbl_Iteration i
        WHERE   i.PartitionId = @partitionId
    )
    UPDATE  TOP(@batchSizeMax) i
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            StartDate = NewStartDate,
            EndDate = NewEndDate
    FROM    Iteration i
    WHERE   NOT EXISTS (
            SELECT  StartDate, -- ok to compare as datetimeoffset because iteration start and end dates are forced to 0:00 and 23:59
                    EndDate
            INTERSECT
            SELECT  NewStartDate,
                    NewEndDate
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT
    SET @complete = IIF (@updatedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

