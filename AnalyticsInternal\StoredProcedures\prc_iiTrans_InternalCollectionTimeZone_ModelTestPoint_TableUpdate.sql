/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 392F6425495397AEE0CBA3BA05E205E7CBD0D63D
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelTestPoint_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @pointIdStart BIGINT = ISNULL(@stateData, 0)
    DECLARE @pointIdEnd BIGINT
    DECLARE @pointCount INT

    SELECT @pointIdEnd = MAX(TestPointId), @pointCount = COUNT(*)
    FROM
    (
        SELECT  TOP (@batchSizeMax) TestPointId
        FROM    AnalyticsModel.tbl_TestPoint
        WHERE   PartitionId = @partitionId
                AND TestPointId > @pointIdStart
        ORDER   BY TestPointId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @pointIdEnd
    SET @complete = IIF(@pointCount < @batchSizeMax, 1, 0)

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            ChangedDate = ChangedDate AT TIME ZONE @timeZone,
            ChangedDateSK = AnalyticsInternal.func_GenDateSK(ChangedDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_TestPoint t
    WHERE   PartitionId = @partitionId
            AND TestPointId BETWEEN @pointIdStart AND @pointIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(ChangedDate AS DATETIME)
                INTERSECT
                SELECT
                CAST(ChangedDate AT TIME ZONE @timeZone AS DATETIME)
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

