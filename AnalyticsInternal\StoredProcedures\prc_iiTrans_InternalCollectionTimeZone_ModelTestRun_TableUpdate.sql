/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A134B1F50D664064481BD2ECFBAEF00549A9080A
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelTestRun_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @runSKStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @runSKEnd BIGINT
    DECLARE @runCount BIGINT

    SELECT @runSKEnd = MAX(TestRunSK), @runCount = COUNT(*)
    FROM
    (
        SELECT  TOP (@batchSizeMax) TestRunSK
        FROM    AnalyticsModel.tbl_TestRun
        WHERE   PartitionId = @partitionId
                AND TestRunSK >= @runSKStart
        ORDER   BY TestRunSK
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @runSKEnd
    SET @complete = IIF(@runCount < @batchSizeMax, 1, 0)

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            StartedDate = StartedDate AT TIME ZONE @timeZone,
            StartedDateSK = AnalyticsInternal.func_GenDateSK(StartedDate AT TIME ZONE @timeZone),
            CompletedDate = CompletedDate AT TIME ZONE @timeZone,
            CompletedDateSK = AnalyticsInternal.func_GenDateSK(CompletedDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_TestRun t
    WHERE   PartitionId = @partitionId
            AND TestRunSK BETWEEN @runSKStart AND @runSKEnd
            AND NOT EXISTS (
                SELECT
                CAST(StartedDate AS DATETIME),
                CAST(CompletedDate AS DATETIME)
                INTERSECT
                SELECT
                CAST(StartedDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(CompletedDate AT TIME ZONE @timeZone AS DATETIME)
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

