/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 77362B95FA2CB310DD9E2EC22E9598BAEB9FC692
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelWorkItemDescendantHistory_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @workItemIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @workItemIdEnd BIGINT
    DECLARE @maxWorkItemId BIGINT

    SELECT @workItemIdEnd = MAX(WorkItemId)
    FROM
    (
        SELECT  TOP (@batchSizeMax) WorkItemId
        FROM    AnalyticsModel.tbl_WorkItemDescendantHistory
        WHERE   PartitionId = @partitionId
                AND WorkItemId >= @workItemIdStart
        ORDER   BY WorkItemId
    ) L
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT  @maxWorkItemId = MAX(WorkItemId)
    FROM    AnalyticsModel.tbl_WorkItemDescendantHistory
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @workItemIdEnd
    SET @complete = IIF(@workItemIdEnd < @maxWorkItemId, 0, 1)

    UPDATE  l
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            CreatedDate = l.CreatedDate AT TIME ZONE @timeZone,
            DeletedDate = l.DeletedDate AT TIME ZONE @timeZone,
            CreatedDateSK = AnalyticsInternal.func_GenDateSK(l.CreatedDate AT TIME ZONE @timeZone),
            DeletedDateSK = AnalyticsInternal.func_GenDateSK(l.DeletedDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_WorkItemDescendantHistory l
    WHERE   l.PartitionId = @partitionId
            AND WorkItemId BETWEEN @workItemIdStart AND @workItemIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(CreatedDate AS DATETIME2),
                CAST(DeletedDate AS DATETIME2)
                INTERSECT
                SELECT
                CAST(CreatedDate AT TIME ZONE @timeZone AS DATETIME2),
                CAST(DeletedDate AT TIME ZONE @timeZone AS DATETIME2)
            )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    -- set IsLastRevisionOfDay
    ;WITH AllLinks AS
    (
        SELECT  *,
                CAST(CreatedDate AS DATE) CreatedDay,
                CAST(DeletedDate AS DATE) DeletedDay
        FROM    AnalyticsModel.tbl_WorkItemDescendantHistory
        WHERE   PartitionId = @partitionId
                AND WorkItemId BETWEEN @workItemIdStart AND @workItemIdEnd
                AND AnalyticsBatchId = @batchId
    )
    UPDATE  t
    SET     IsLastRevisionOfPeriod = IIF(DeletedDay IS NULL, 2047,
                  IIF(DATEDIFF(DAY, CreatedDay, DeletedDay) > 0, 1, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -0, CreatedDay), DATEADD(DAY, -0, DeletedDay)) > 0, 128, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -1, CreatedDay), DATEADD(DAY, -1, DeletedDay)) > 0, 2, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -2, CreatedDay), DATEADD(DAY, -2, DeletedDay)) > 0, 4, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -3, CreatedDay), DATEADD(DAY, -3, DeletedDay)) > 0, 8, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -4, CreatedDay), DATEADD(DAY, -4, DeletedDay)) > 0, 16, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -5, CreatedDay), DATEADD(DAY, -5, DeletedDay)) > 0, 32, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -6, CreatedDay), DATEADD(DAY, -6, DeletedDay)) > 0, 64, 0)
                | IIF(DATEDIFF(MONTH,   CreatedDay, DeletedDay) > 0, 256, 0)
                | IIF(DATEDIFF(QUARTER, CreatedDay, DeletedDay) > 0, 512, 0)
                | IIF(DATEDIFF(YEAR,    CreatedDay, DeletedDay) > 0, 1024, 0)
                )
    FROM    AllLinks t

    RETURN 0
END

GO

