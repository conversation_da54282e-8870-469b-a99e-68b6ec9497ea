/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B3E2A2A13B502676A974D80E828368E5D0C2D55C
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelWorkItemLinkHistory_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @sourceWorkItemIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @sourceWorkItemIdEnd BIGINT
    DECLARE @maxSourceWorkItemId BIGINT

    SELECT @sourceWorkItemIdEnd = MAX(SourceWorkItemId)
    FROM
    (
        SELECT  TOP (@batchSizeMax) SourceWorkItemId
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory
        WHERE   PartitionId = @partitionId
                AND SourceWorkItemId >= @sourceWorkItemIdStart
        ORDER   BY SourceWorkItemId
    ) L
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT  @maxSourceWorkItemId = MAX(SourceWorkItemId)
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @sourceWorkItemIdEnd
    SET @complete = IIF(@sourceWorkItemIdEnd < @maxSourceWorkItemId, 0, 1)

    UPDATE  l
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            CreatedDate = l.CreatedDate AT TIME ZONE @timeZone,
            DeletedDate = l.DeletedDate AT TIME ZONE @timeZone
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory l
    WHERE   l.PartitionId = @partitionId
            AND SourceWorkItemId BETWEEN @sourceWorkItemIdStart AND @sourceWorkItemIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(CreatedDate AS DATETIME),
                CAST(DeletedDate AS DATETIME)
                INTERSECT
                SELECT
                CAST(CreatedDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(DeletedDate AT TIME ZONE @timeZone AS DATETIME)
            )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

