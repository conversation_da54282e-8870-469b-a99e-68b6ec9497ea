/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 950F3BBC6B588BBAECF765ABBC670330B3F4DBC6
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalProcessField_ModelWorkItem_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    SET @insertedCount = 0
    SET @updatedCount = 0

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    DECLARE @batchRangeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchWorkItemIdRangeMax'), 50000)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    -- List of fields that were changed
    CREATE TABLE #ChangedFields
    (
        FieldName           NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        SourceFieldName     NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        --SourceKeyFieldName  NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        FieldType           NVARCHAR(50) COLLATE DATABASE_DEFAULT,
        ModelTableName      NVARCHAR(50) COLLATE DATABASE_DEFAULT,
        ModelColumnName     NVARCHAR(50) COLLATE DATABASE_DEFAULT,
        FieldSK             INT       PRIMARY KEY
    )

    -- Using temp table instead of table variable as in staging, because size of fields in question much bigger when processing backlog
    INSERT INTO #ChangedFields
    SELECT DISTINCT
            FieldName,
            pf.SourceFieldName,
            --pf.SourceKeyFieldName,
            pf.FieldType,
            pf.ModelTableName,
            pf.ModelColumnName,
            f.FieldSK
    FROM    AnalyticsInternal.tbl_ProcessField pf
    JOIN    AnalyticsInternal.tbl_Fields f
    ON      pf.PartitionId = f.PartitionId
            AND (pf.SourceFieldName = f.FieldName) -- OR pf.SourceKeyFieldName = f.FieldName)
    WHERE   pf.PartitionId = @partitionId
            AND pf.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND pf.IsDeleted = 0
            AND pf.ModelTableName IS NOT NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- ignore fields that were previuosly processed
    DELETE  c
    FROM    #ChangedFields c
    JOIN    AnalyticsInternal.tbl_ProcessField pf
    --ON      (pf.SourceFieldName = c.FieldName OR pf.SourceKeyFieldName = c.FieldName)
    ON      pf.SourceFieldName = c.FieldName
    WHERE   pf.PartitionId = @partitionId
            AND pf.AnalyticsBatchId < @triggerBatchIdStart
            AND pf.IsDeleted = 0
            AND pf.ModelTableName IS NOT NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @CustomTables TABLE
    (
        ModelTableName NVARCHAR(50)
    )

    -- Get tables for custom fields that have to be processed
    INSERT INTO @CustomTables
    SELECT DISTINCT f.ModelTableName
    FROM #ChangedFields f

    -- find a suitable sub batch based on desired row count
    -- Note about Performance: Forcing the clustered index here is to prevent the use of IX_tbl_WorkItemRevisionExtended_FieldSK_SystemId.
    -- Onboarding large accounts will often have the same number of rows in both changedfields and the extended table, but
    -- in testing with 1 row in changedfields, the NCI appears to offer worse performance than the clustered index.
    DECLARE @workItemIdRangeStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @workItemIdRangeEnd INT = @workItemIdRangeStart + @batchRangeMax
    DECLARE @subBatchStart INT, @subBatchEnd INT, @subBatchMax INT = **********

    SELECT TOP 1 @subBatchStart = MIN(System_Id) OVER (ORDER BY wi.System_Id)
                , @subBatchEnd = LEAD(System_Id, @batchSizeMax - 1) OVER (ORDER BY wi.System_Id)
    FROM    AnalyticsStage.tbl_WorkItemRevisionExtended wi WITH (INDEX (CI_tbl_WorkItemRevisionExtended))
    JOIN    #ChangedFields f
    ON      wi.PartitionId = @partitionId
            AND wi.FieldSK = f.FieldSK
            AND wi.System_Id >= @workItemIdRangeStart
            AND wi.System_Id <= @workItemIdRangeEnd
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@subBatchEnd IS NULL)
    BEGIN
        SELECT  @subBatchMax = MAX(System_Id), @subBatchEnd = @workItemIdRangeEnd
        FROM    #ChangedFields f
        JOIN    AnalyticsStage.tbl_WorkItemRevisionExtended wi
        ON      wi.PartitionId = @partitionId
                AND wi.FieldSK = f.FieldSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @endStateData = @subBatchEnd
    SET @complete = IIF(@subBatchEnd < @subBatchMax, 0, 1)

    -- Iterate via tables and fill them (without cursors)
    WHILE @subBatchStart IS NOT NULL AND EXISTS(SELECT * FROM @CustomTables)
    BEGIN
        DECLARE @customTableName NVARCHAR(50)

        SELECT TOP(1) @customTableName = ModelTableName FROM @CustomTables

        DECLARE @pivotSelectClause NVARCHAR(MAX) = ''
        DECLARE @pivotAlias NVARCHAR(MAX) = ''
        DECLARE @updateClause NVARCHAR(MAX) = ''
        DECLARE @newLine CHAR(2) =  CHAR(10) + CHAR(13)
        DECLARE @insertFieldsList NVARCHAR(MAX) = ''
        DECLARE @insertValuesList NVARCHAR(MAX) = ''
        DECLARE @fieldsList NVARCHAR(MAX) = ''
        DECLARE @fieldsListWithType NVARCHAR(MAX) = ''
        DECLARE @sColumns NVARCHAR(MAX) = ''
        DECLARE @tColumns NVARCHAR(MAX) = ''

        SELECT  @fieldsList += CAST(FieldSK AS NVARCHAR(10)) + ','
        FROM
        (
            SELECT  DISTINCT FieldSK
            FROM    #ChangedFields
            WHERE   ModelTableName = @customTableName
        ) T;

        SELECT
            @pivotSelectClause += ', ' +
                CASE FieldType
                    WHEN 'String'    THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(256))'
                    WHEN 'Integer'   THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS BIGINT)'
                    WHEN 'DateTime'  THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS DATETIMEOFFSET) AT TIME ZONE @timeZone'
                    WHEN 'Double'    THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS FLOAT)'
                    WHEN 'Boolean'   THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS BIT)'
                    WHEN 'Identity'  THEN 'IIF (ISJSON(CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(4000))) > 0, AnalyticsInternal.func_GetUserSKFromWITPerson(CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(4000))), NULL)'
                END + ' AS ' + QUOTENAME(ModelColumnName) + @newLine,
            @updateClause += ', t.' + QUOTENAME(ModelColumnName)+ ' = s.' + QUOTENAME(ModelColumnName) + @newLine,
            @insertFieldsList += ', ' + QUOTENAME(ModelColumnName) + @newLine,
            @insertValuesList += ', s.' + QUOTENAME(ModelColumnName) + @newLine,
            @fieldsListWithType +=
                CASE FieldType
                    WHEN 'String'    THEN QUOTENAME(ModelColumnName) + ' NVARCHAR(256) COLLATE DATABASE_DEFAULT,'+ @newLine
                    WHEN 'Integer'   THEN QUOTENAME(ModelColumnName) + ' BIGINT,'+ @newLine
                    WHEN 'DateTime'  THEN QUOTENAME(ModelColumnName) + ' DATETIMEOFFSET,'+ @newLine
                    WHEN 'Double'    THEN QUOTENAME(ModelColumnName) + ' FLOAT,'+ @newLine
                    WHEN 'Boolean'   THEN QUOTENAME(ModelColumnName) + ' BIT,'+ @newLine
                    WHEN 'Identity'  THEN QUOTENAME(ModelColumnName) + ' UNIQUEIDENTIFIER,'+ @newLine
                END,
            @pivotAlias +=  QUOTENAME(SourceFieldName) + ', ',
            @sColumns += ', s.' + QUOTENAME(ModelColumnName) + @newLine,
            @tColumns += ', t.' + QUOTENAME(ModelColumnName) + @newLine

        FROM
        (
            SELECT  DISTINCT
                    SourceFieldName,
                    --SourceKeyFieldName,
                    FieldType,
                    ModelColumnName
            FROM    #ChangedFields
            WHERE   ModelTableName = @customTableName
        ) T;

        -- SDL Exception: Column names as well as tables in merge statement are dynamic
        -- However source of columns and tables in tbl_ProcessField that is curated
        DECLARE @cmd NVARCHAR(MAX) = '
        DECLARE @processedTable TABLE
        (
            WorkItemId      INT,
            Revision        INT,
            MergeAction     NVARCHAR(10)
        )

        -- Creating temp table to avoid hash joins or any other bad join
        CREATE TABLE #Src
        (
            WorkItemId                  INT NOT NULL,
            Revision                    INT NOT NULL,
            WorkItemRevisionSK          INT NOT NULL,
            <FIELDS_LIST_WITH_TYPE>
            PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
        )

        INSERT INTO #Src
        SELECT  WorkItemId
                , Revision
                , WorkItemRevisionSK
                <SELECT_CLAUSE>
        FROM
        (
            SELECT  System_Id AS WorkItemId
                    , System_Rev AS Revision
                    , WorkItemRevisionSK
                    , FieldName
                    --, IIF(IsIdentity = 1, CAST(CAST(we.ValueObject AS NVARCHAR(4000)) AS SQL_VARIANT), COALESCE(CAST(we.ValueString AS SQL_VARIANT), CAST(we.ValueGuid AS SQL_VARIANT), CAST(we.ValueInt AS SQL_VARIANT), CAST(we.ValueFloat AS SQL_VARIANT), CAST(we.ValueDateTime AS SQL_VARIANT), CAST(CAST(we.ValueObject AS NVARCHAR(4000)) AS SQL_VARIANT))) AS Value
                    , COALESCE(CAST(we.ValueString AS SQL_VARIANT), CAST(we.ValueGuid AS SQL_VARIANT), CAST(we.ValueInt AS SQL_VARIANT), CAST(we.ValueFloat AS SQL_VARIANT), CAST(we.ValueDateTime AS SQL_VARIANT), CAST(CAST(we.ValueObject AS NVARCHAR(4000)) AS SQL_VARIANT)) AS Value
            FROM    AnalyticsStage.tbl_WorkItemRevisionExtended we
            INNER LOOP JOIN AnalyticsInternal.vw_WorkItemRevisionSK vsk
            ON      vsk.PartitionId = we.PartitionId
                    AND we.System_Id = vsk.WorkItemId
                    AND we.System_Rev = vsk.Revision
            CROSS APPLY
                (
                -- Doing filtering on tbl_WorkItemRevisionExtended and then getting FieldName with cross apply with top(1) to avoid hash joins or any other bad join
                    SELECT TOP(1) FieldName, IIF(FieldType = ''<IDENTITY_FIELD_TYPE>'', 1, 0) AS IsIdentity FROM #ChangedFields cf WHERE cf.FieldSK = we.FieldSK
                ) TCF
            WHERE   we.PartitionId = @partitionId
                    AND we.System_Id BETWEEN @subBatchStart AND @subBatchEnd
                    AND we.FieldSK IN (<FIELDS_LIST>)
        ) T
        PIVOT
        (
            MAX(Value)
            FOR FieldName IN (<PIVOT_AS_ALIAS>)
        ) AS pvt
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1)

        MERGE <TABLE_NAME> as t
        USING #Src as s
            ON (
                    t.PartitionId            = @partitionId
                    AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            )
        WHEN MATCHED AND NOT EXISTS
            (
            SELECT  s.WorkItemRevisionSK
                    <S_COLUMNS>
            INTERSECT
            SELECT  t.WorkItemRevisionSK
                    <T_COLUMNS>
            )
        THEN UPDATE SET
            AnalyticsUpdatedDate = @batchDt
            ,AnalyticsBatchId = @batchId
            <UPDATE_CLAUSE>
        WHEN NOT MATCHED BY TARGET THEN
        INSERT (
            PartitionId
                , AnalyticsBatchId
                , AnalyticsCreatedDate
                , AnalyticsUpdatedDate
                , WorkItemId
                , Revision
                , WorkItemRevisionSK
            <INSERT_FIELDS_LIST>
        )
        VALUES (
             @partitionId
             , @batchId
             , @batchDt
             , @batchDt
             , WorkItemId
             , Revision
             , WorkItemRevisionSK
             <INSERT_VALUES_LIST>
        )
        OUTPUT INSERTED.WorkItemId, INSERTED.Revision, $action INTO @processedTable
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        DROP TABLE IF EXISTS #Src

        SET @inserted = (SELECT COUNT(*) FROM @processedTable WHERE MergeAction = ''INSERT'')
        SET @updated = (SELECT COUNT(*) FROM @processedTable WHERE MergeAction = ''UPDATE'')
        '

        --@fieldsListWithType-3 is to remove an extra newline (2 characters) and a comma (1 character) in the end.
        SET @cmd = REPLACE(@cmd,'<FIELDS_LIST_WITH_TYPE>',LEFT(@fieldsListWithType, LEN(@fieldsListWithType)-3))
        SET @cmd = REPLACE(@cmd,'<SELECT_CLAUSE>',@pivotSelectClause)
        SET @cmd = REPLACE(@cmd,'<PIVOT_AS_ALIAS>', LEFT(@pivotAlias, LEN(@pivotAlias) - 1))
        SET @cmd = REPLACE(@cmd,'<TABLE_NAME>', '[AnalyticsModel].' +QUOTENAME('tbl_' + @customTableName))
        SET @cmd = REPLACE(@cmd,'<UPDATE_CLAUSE>',@updateClause)
        SET @cmd = REPLACE(@cmd,'<INSERT_FIELDS_LIST>',@insertFieldsList)
        SET @cmd = REPLACE(@cmd,'<INSERT_VALUES_LIST>',@insertValuesList)
        SET @cmd = REPLACE(@cmd,'<FIELDS_LIST>',LEFT(@fieldsList, LEN(@fieldsList) - 1))
        SET @cmd = REPLACE(@cmd,'<S_COLUMNS>',@sColumns)
        SET @cmd = REPLACE(@cmd,'<T_COLUMNS>',@tColumns)
        SET @cmd = REPLACE(@cmd,'<IDENTITY_FIELD_TYPE>', 'Identity')

        DECLARE @insertedStep INT
        DECLARE @updatedStep INT

        EXEC sp_executesql @cmd,
            N'@partitionId INT, @batchId INT, @batchDt DATETIME2, @batchSizeMax INT, @subBatchStart INT, @subBatchEnd INT, @timeZone NVARCHAR(128), @inserted INT OUTPUT, @updated INT OUTPUT',
            @partitionId = @partitionId, @batchId =@batchId, @batchDt = @batchDt, @batchSizeMax = @batchSizeMax, @subBatchStart = @subBatchStart, @subBatchEnd = @subBatchEnd, @timeZone = @timeZone, @inserted = @insertedStep OUTPUT, @updated = @updatedStep OUTPUT

        SET @insertedCount += @insertedStep
        SET @updatedCount += @updatedStep

        DELETE FROM @CustomTables WHERE @customTableName = ModelTableName
    END

    RETURN 0
END

GO

