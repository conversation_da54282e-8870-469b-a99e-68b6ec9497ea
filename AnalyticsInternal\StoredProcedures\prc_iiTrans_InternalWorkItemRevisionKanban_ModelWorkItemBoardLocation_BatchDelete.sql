/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D3BB3A5946D92F3048F6BCC7BA815140A2915A52
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalWorkItemRevisionKanban_ModelWorkItemBoardLocation_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedRev (System_Id INT, System_Rev INT, ExtensionId UNIQUEIDENTIFIER)
    CREATE TABLE #DeletedWibl (WorkItemRevisionSK INT, BoardLocationSK INT)

    IF (@subBatchCount = 1)
    BEGIN
        INSERT  #DeletedRev (System_Id, System_Rev, ExtensionId)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev, ExtensionId
        FROM    AnalyticsInternal.tbl_WorkItemRevisionKanban_Deleted d WITH (INDEX (IX_tbl_WorkItemRevisionKanban_AxBatchIdDeleted))
        WHERE   d.PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.System_Id > ISNULL(@stateData, -1)
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        INSERT  #DeletedRev (System_Id, System_Rev, ExtensionId)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev, ExtensionId
        FROM    AnalyticsInternal.tbl_WorkItemRevisionKanban_Deleted d WITH (INDEX (CI_tbl_WorkItemRevisionKanban_Deleted))
        WHERE   d.PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.System_Id > ISNULL(@stateData, -1)
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(System_Id) FROM #DeletedRev)

    DELETE  d
    FROM    #DeletedRev d
    JOIN    AnalyticsInternal.tbl_WorkItemRevisionKanban s
    ON      s.PartitionId = @partitionId
            AND s.System_Id = d.System_Id
            AND s.System_Rev = d.System_Rev
            AND s.ExtensionId = d.ExtensionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #DeletedWibl (WorkItemRevisionSK, BoardLocationSK)
    SELECT  r.WorkItemRevisionSK, l.BoardLocationSK
    FROM    #DeletedRev d
    JOIN    AnalyticsInternal.vw_WorkItemRevisionSK r
    ON      @partitionId = r.PartitionId
            AND d.System_Id = r.WorkItemId
            AND d.System_Rev = r.Revision
    JOIN    AnalyticsModel.tbl_BoardLocation l
    ON      @partitionId = l.PartitionId
            AND d.ExtensionId = l.BoardExtensionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    #DeletedWibl d
    JOIN    AnalyticsModel.tbl_WorkItemBoardLocation t
    ON      @partitionId = t.PartitionId
            AND d.WorkItemRevisionSK = t.WorkItemRevisionSK
            AND d.BoardLocationSK = t.BoardLocationSK
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedRev
    DROP TABLE #DeletedWibl

    RETURN 0
END

GO

