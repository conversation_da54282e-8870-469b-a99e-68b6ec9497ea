/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 335ECCA822AE6879519C612F798D9547F6B787E2
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalWorkItemTypeState_ModelWorkItem_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 50000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, NULL, 0) -- don't lower batch size for previous failures

    DECLARE @projectSK UNIQUEIDENTIFIER = NULL
    DECLARE @workItemType NVARCHAR(256) = NULL
    DECLARE @workItemState NVARCHAR(256) = NULL
    DECLARE @stateCategory NVARCHAR(256) = NULL
    DECLARE @provisionalEndStateData BIGINT = @stateData

    -- this will grab one changed row from the trigger table
    -- the checksum provides a way to walk the table, and
    -- handle skipping deletions from the trigger between sub batches
   ;WITH Wits AS
    (
        SELECT  ProjectSK, WorkItemType, [State], StateCategory, CHECKSUM(*) AS ChkSum
        FROM    AnalyticsInternal.tbl_WorkItemTypeState
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    )
    SELECT  TOP 1 @projectSK = ProjectSK,
            @workItemType = WorkItemType,
            @workItemState = [State],
            @stateCategory = StateCategory,
            @provisionalEndStateData = ChkSum
    FROM    Wits
    WHERE   @stateData IS NULL
            OR ChkSum > @stateData
    ORDER BY ChkSum ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = IIF(@@ROWCOUNT = 0, 1, 0)

    CREATE TABLE #WorkItemRevs
    (
        WorkItemId INT,
        Revision INT
    )

    CREATE CLUSTERED INDEX CI_WorkItemRevs ON #WorkItemRevs (WorkItemId, Revision)

    -- attempt to use CCI index directly if available
    IF EXISTS (SELECT * FROM sys.indexes WHERE type = 5 AND name = 'CL_AnalyticsModel_tbl_WorkItem' AND object_id = OBJECT_ID('AnalyticsModel.tbl_WorkItem'))
    BEGIN
        -- no top query
        -- this peforms better
        -- better to get them all and trim to max batch size
        CREATE TABLE #WorkItemIdsFast
        (
            WorkItemId INT PRIMARY KEY
        )

        CREATE TABLE #WorkItemRevsFast
        (
            WorkItemId INT,
            Revision INT
        )

        CREATE CLUSTERED INDEX CI_WorkItemRevsFast ON #WorkItemRevsFast (WorkItemId, Revision)

        -- first gather impacted work item ids - this is faster than gathering id, revs from CCI
        -- and scales better for large accounts
        INSERT  #WorkItemIdsFast
        SELECT  WorkItemId
        FROM    AnalyticsModel.tbl_WorkItem WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem))
        WHERE   PartitionId = @partitionId
                AND ProjectSK = @projectSK
                AND WorkItemType = @workItemType
                AND [State] = @workItemState
                AND NOT EXISTS (SELECT StateCategory INTERSECT SELECT @stateCategory)
        UNION
        SELECT  WorkItemId
        FROM    AnalyticsModel.tbl_WorkItemHistory WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory))
        WHERE   PartitionId = @partitionId
                AND ProjectSK = @projectSK
                AND WorkItemType = @workItemType
                AND [State] = @workItemState
                AND NOT EXISTS (SELECT StateCategory INTERSECT SELECT @stateCategory)
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        DECLARE @maxWorkItemId INT = (SELECT MAX(WorkItemId) FROM (SELECT TOP (@batchSizeMax) WorkItemId FROM #WorkItemIdsFast ORDER BY WorkItemId) core)

        -- next gather impacted revs
        INSERT #WorkItemRevsFast
        SELECT  WorkItemId,
                Revision
        FROM    AnalyticsModel.tbl_WorkItemHistory WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory))
        WHERE   PartitionId = @partitionId
                AND ProjectSK = @projectSK
                AND WorkItemType = @workItemType
                AND [State] = @workItemState
                AND NOT EXISTS (SELECT StateCategory INTERSECT SELECT @stateCategory)
                AND WorkItemId <= @maxWorkItemId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        INSERT #WorkItemRevsFast
        SELECT  WorkItemId,
                Revision
        FROM    AnalyticsModel.tbl_WorkItem WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem))
        WHERE   PartitionId = @partitionId
                AND ProjectSK = @projectSK
                AND WorkItemType = @workItemType
                AND [State] = @workItemState
                AND NOT EXISTS (SELECT StateCategory INTERSECT SELECT @stateCategory)
                AND WorkItemId <= @maxWorkItemId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        -- trim to max batch size
        INSERT  #WorkItemRevs
        SELECT  TOP (@batchSizeMax) WITH TIES
                WorkItemId,
                Revision
        FROM    #WorkItemRevsFast
        ORDER BY WorkItemId

        SET @endStateData = IIF(@@ROWCOUNT < @batchSizeMax, @provisionalEndStateData, @stateData)

        DROP TABLE #WorkItemIdsFast
        DROP TABLE #WorkItemRevsFast
    END
    ELSE -- if no CCI, use conventional TOP query
    BEGIN
        INSERT #WorkItemRevs
        SELECT  TOP (@batchSizeMax) WorkItemId,
                Revision
        FROM    AnalyticsModel.vw_WorkItemRevision
        WHERE   PartitionId = @partitionId
                AND ProjectSK = @projectSK
                AND WorkItemType = @workItemType
                AND [State] = @workItemState
                AND NOT EXISTS (SELECT StateCategory INTERSECT SELECT @stateCategory)
        ORDER BY WorkItemId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        SET @endStateData = IIF(@@ROWCOUNT < @batchSizeMax, @provisionalEndStateData, @stateData)
    END

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            StateCategory = @stateCategory
    FROM    AnalyticsModel.tbl_WorkItemHistory t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory))
    JOIN    #WorkItemRevs s
    ON      t.WorkItemId = s.WorkItemId
            AND t.Revision = s.Revision
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            StateCategory = @stateCategory
    FROM    AnalyticsModel.tbl_WorkItem t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem))
    JOIN    #WorkItemRevs s
    ON      t.WorkItemId = s.WorkItemId
            AND t.Revision = s.Revision
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount += @@ROWCOUNT

    -- we have changed revisions - now look at downstream revisions to recompute InProgress and Completed dates, and related measures
    CREATE TABLE #WorkItemStateCategoryChange
    (
        System_Id INT NOT NULL,
        System_Rev INT NOT NULL,
        System_ChangedDate DATETIMEOFFSET NULL,
        StateCategory NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL
    )
    CREATE CLUSTERED INDEX CI_WorkItemStateCategoryChange ON #WorkItemStateCategoryChange (System_Id, System_Rev)

    INSERT #WorkItemStateCategoryChange
    SELECT System_Id, System_Rev, System_ChangedDate, StateCategory
    FROM
        (
        SELECT  wi.System_Id, System_Rev, System_ChangedDate, StateCategory, LAG(StateCategory) OVER (PARTITION BY wi.PartitionId, wi.System_Id ORDER BY wi.System_Rev) AS PreviousStateCategory
        FROM    (SELECT DISTINCT WorkItemId FROM #WorkItemRevs) tid
        JOIN    AnalyticsStage.tbl_WorkItemRevision wi ON wi.System_Id = tid.WorkItemId
        LEFT JOIN AnalyticsInternal.tbl_WorkItemTypeState scm
        ON      scm.PartitionId = wi.PartitionId
                AND scm.ProjectSK = wi.System_ProjectGuid
                AND scm.WorkItemType = wi.System_WorkItemType
                AND scm.[State] = wi.System_State
        WHERE   wi.PartitionId = @partitionId
        ) core
    WHERE   ISNULL(StateCategory, '') <> ISNULL(PreviousStateCategory, '') OR System_Rev = 1
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    -- first, gather existing values for downstream revs
    CREATE TABLE #WorkItemRevsAll
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        PrevInProgressDate DATETIMEOFFSET NULL,
        PrevCompletedDate DATETIMEOFFSET NULL,
        InProgressDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        CompletedDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
    )
    CREATE CLUSTERED INDEX CI_WorkItemRevsAll ON #WorkItemRevsAll (WorkItemId, Revision)

    INSERT #WorkItemRevsAll (WorkItemId, Revision, PrevInProgressDate, PrevCompletedDate)
    SELECT
        t.WorkItemId,
        t.Revision,
        t.InProgressDate,
        t.CompletedDate
    FROM AnalyticsModel.tbl_WorkItemHistory t
    JOIN (SELECT WorkItemId, MIN(Revision) RevisionMin FROM #WorkItemRevs GROUP BY WorkItemId) tid ON tid.WorkItemId = t.WorkItemId AND tid.RevisionMin <= t.Revision
    WHERE t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT #WorkItemRevsAll (WorkItemId, Revision, PrevInProgressDate, PrevCompletedDate)
    SELECT
        t.WorkItemId,
        t.Revision,
        t.InProgressDate,
        t.CompletedDate
    FROM AnalyticsModel.tbl_WorkItem t
    JOIN (SELECT WorkItemId, MIN(Revision) RevisionMin FROM #WorkItemRevs GROUP BY WorkItemId) tid ON tid.WorkItemId = t.WorkItemId AND tid.RevisionMin <= t.Revision
    WHERE t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- set inprogress and completed
    UPDATE  r
    SET     InProgressDateRaw = stateDate.InProgressDate,
            CompletedDateRaw = IIF(stateDate.CompletedDate < stateDate.ReworkDate, NULL, stateDate.CompletedDate)
    FROM    #WorkItemRevsAll r
    OUTER APPLY
    (
        SELECT  MIN(IIF(ISNULL(StateCategory, 'Proposed') <> 'Proposed', System_ChangedDate, NULL)) AS InProgressDate, -- treat NULL as Proposed
                MAX(IIF(StateCategory = 'Complete' OR StateCategory = 'Completed', System_ChangedDate, NULL)) AS CompletedDate,
                MAX(IIF(StateCategory <> 'Complete' AND StateCategory <> 'Completed', System_ChangedDate, NULL)) AS ReworkDate -- ignoring NULLs
        FROM    #WorkItemStateCategoryChange
        WHERE   System_Id = r.WorkItemId
                AND System_Rev <= r.Revision
    ) stateDate

    -- delete unchagned rows
    DELETE  s
    FROM    #WorkItemRevsAll AS s
    WHERE   ((InProgressDateRaw IS NULL AND PrevInProgressDate IS NULL) OR InProgressDateRaw = PrevInProgressDate)
            AND ((CompletedDateRaw IS NULL AND PrevCompletedDate IS NULL) OR CompletedDateRaw = PrevCompletedDate)

    -- update target

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            InProgressDate      = s.InProgressDateRaw AT TIME ZONE @timeZone,
            InProgressDateSK    = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw AT TIME ZONE @timeZone),
            CompletedDate       = s.CompletedDateRaw AT TIME ZONE @timeZone,
            CompletedDateSK     = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw AT TIME ZONE @timeZone),
            LeadTimeDays        = DATEDIFF(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0,
            CycleTimeDays       = DATEDIFF(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0
    FROM    AnalyticsModel.tbl_WorkItemHistory t  WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory))
    JOIN    #WorkItemRevsAll s
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            InProgressDate      = s.InProgressDateRaw AT TIME ZONE @timeZone,
            InProgressDateSK    = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw AT TIME ZONE @timeZone),
            CompletedDate       = s.CompletedDateRaw AT TIME ZONE @timeZone,
            CompletedDateSK     = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw AT TIME ZONE @timeZone),
            LeadTimeDays        = DATEDIFF(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0,
            CycleTimeDays       = DATEDIFF(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0
    FROM    AnalyticsModel.tbl_WorkItem t  WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem))
    JOIN    #WorkItemRevsAll s
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DROP TABLE #WorkItemRevs
    DROP TABLE #WorkItemRevsAll

    RETURN 0

END

GO

