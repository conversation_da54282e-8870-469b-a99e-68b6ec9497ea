/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 99344DEDCEADDFDE85FAE6DB12BDAC9538BEC08D
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelArea_ModelTeamArea_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ImpactedArea (AreaSK UNIQUEIDENTIFIER)

    INSERT  #ImpactedArea
    SELECT  AreaSK
    FROM    AnalyticsModel.tbl_Area a
    WHERE   a.PartitionId = @partitionId
            AND a.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    CREATE TABLE #Src (TeamSK UNIQUEIDENTIFIER, AreaSK UNIQUEIDENTIFIER, Depth INT)

    INSERT #Src
    SELECT  ta.TeamGuid AS TeamSK,
            a.AreaSK,
            MAX(a.Depth) AS Depth -- When Area and its ancestor with IncludeChildren both included into settings peek deepest
    FROM    AnalyticsModel.tbl_Area a
    JOIN    AnalyticsModel.tbl_Area aa
    ON      aa.PartitionId = a.PartitionId
            AND (aa.Depth < a.Depth OR a.AreaSK = aa.AreaSK)
            AND (aa.AreaLevel1 = a.AreaLevel1)
            AND (aa.Depth < 1 OR aa.AreaLevel2 = a.AreaLevel2)
            AND (aa.Depth < 2 OR aa.AreaLevel3 = a.AreaLevel3)
            AND (aa.Depth < 3 OR aa.AreaLevel4 = a.AreaLevel4)
            AND (aa.Depth < 4 OR aa.AreaLevel5 = a.AreaLevel5)
            AND (aa.Depth < 5 OR aa.AreaLevel6 = a.AreaLevel6)
            AND (aa.Depth < 6 OR aa.AreaLevel7 = a.AreaLevel7)
            AND (aa.Depth < 7 OR aa.AreaLevel8 = a.AreaLevel8)
            AND (aa.Depth < 8 OR aa.AreaLevel9 = a.AreaLevel9)
            AND (aa.Depth < 9 OR aa.AreaLevel10 = a.AreaLevel10)
            AND (aa.Depth < 10 OR aa.AreaLevel11 = a.AreaLevel11)
            AND (aa.Depth < 11 OR aa.AreaLevel12 = a.AreaLevel12)
            AND (aa.Depth < 12 OR aa.AreaLevel13 = a.AreaLevel13)
            AND (aa.Depth < 13 OR aa.AreaLevel14 = a.AreaLevel14)
    JOIN
        (
        SELECT  t.PartitionId,
                t.TeamGuid,
                a.Item.value('Guid[1]','uniqueidentifier') AS AreaId,
                ISNULL(a.Item.value('IncludeChildren[1]','bit'), 0) AS IncludeChildren
        FROM    AnalyticsStage.tbl_TeamSetting t
        CROSS APPLY Areas.nodes('//Item') a(Item)
        ) ta
    ON      ta.PartitionId = aa.PartitionId
            AND ta.AreaId = aa.AreaSK
    WHERE   a.PartitionId = @partitionId
            AND a.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND (ta.AreaId = a.AreaSK OR ta.IncludeChildren = 1)
    GROUP BY ta.PartitionId,
            ta.TeamGuid,
            a.AreaSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER);

    ;WITH Tgt AS
    (
        -- MERGE has a bug where it tries accessing entities in all partitions (even offline ones) for the target table.
        -- To overcome this problem the target table has to be pre-filtered by correct partition id.
        SELECT * FROM AnalyticsModel.tbl_TeamArea WHERE PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) Tgt AS t
    USING   #Src AS s
    ON      t.TeamSK = s.TeamSK
            AND t.AreaSK = s.AreaSK
    WHEN MATCHED AND NOT EXISTS (SELECT s.Depth INTERSECT SELECT t.Depth) THEN
    UPDATE SET AnalyticsBatchId = @batchId,
               AnalyticsUpdatedDate = @batchDt,
               Depth = s.Depth
    WHEN NOT MATCHED BY TARGET
    THEN INSERT
        (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        TeamSK,
        AreaSK,
        Depth
        )
    VALUES
        (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TeamSK,
        s.AreaSK,
        s.Depth
        )
    WHEN NOT MATCHED BY SOURCE
    AND t.AreaSK IN (SELECT AreaSK FROM #ImpactedArea)
    THEN DELETE
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #Src
    DROP TABLE #ImpactedArea

    RETURN 0
END

GO

