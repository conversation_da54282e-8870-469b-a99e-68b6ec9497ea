/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E6C63CFBF154EA32E50C64090C6F6ECB27062D15
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelArea_ModelTeamArea_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @deleted TABLE
    (
        TeamSK UNIQUEIDENTIFIER,
        AreaSK UNIQUEIDENTIFIER
    )

    DELETE TOP (@batchSizeMax) AnalyticsModel.tbl_TeamArea
    OUTPUT  DELETED.TeamSK, DELETED.AreaSK INTO @deleted
    FROM AnalyticsModel.tbl_TeamArea t
    LEFT JOIN AnalyticsModel.tbl_Area a ON a.PartitionId = t.PartitionId AND a.AreaSK = t.AreaSK
    WHERE t.PartitionId = @partitionId
    AND a.AreaSK IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    INSERT AnalyticsModel.tbl_TeamArea_Deleted
    (
        PartitionId,
        AnalyticsBatchIdDeleted,
        AnalyticsDeletedDate,
        TeamSK,
        AreaSK
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            TeamSK,
            AreaSK
    FROM    @deleted

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

