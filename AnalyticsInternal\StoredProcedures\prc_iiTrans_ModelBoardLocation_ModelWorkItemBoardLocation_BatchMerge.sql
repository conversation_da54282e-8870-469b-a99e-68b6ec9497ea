/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A88F6C56A05C626D7CC8B31A6944D41F4BF964B4
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelBoardLocation_ModelWorkItemBoardLocation_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #ImpactedBoards
    (
        BoardExtensionId UNIQUEIDENTIFIER PRIMARY KEY
    )

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

    CREATE TABLE #ImpactedId
    (
        System_Id INT NOT NULL,
        System_Rev INT NOT NULL
    )

    IF @triggerBatchIdStart < 1
    BEGIN
        -- When retransform we are going to touch all board locations => not need to expensive filter/join
        INSERT  #ImpactedId
        SELECT  DISTINCT TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsInternal.tbl_WorkItemRevisionKanban
        WHERE   PartitionId = @partitionId
                AND System_Id >= @workItemIdStart
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    END
    ELSE
    BEGIN
        INSERT  #ImpactedBoards
        SELECT  DISTINCT BoardExtensionId
        FROM    AnalyticsModel.tbl_BoardLocation l
        WHERE   l.PartitionId = @partitionId
                AND l.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND BoardExtensionId IS NOT NULL
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        INSERT  #ImpactedId
        SELECT  DISTINCT TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsInternal.tbl_WorkItemRevisionKanban wi
        JOIN    #ImpactedBoards b
        ON      wi.ExtensionId = b.BoardExtensionId
        WHERE   wi.PartitionId = @partitionId
                AND System_Id >= @workItemIdStart
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    END

    SET @endStateData = (SELECT MAX(System_Id) FROM #ImpactedId)

    CREATE TABLE #ImpactedRev
    (
        WorkItemRevisionSK INT NOT NULL,
        ExtensionId UNIQUEIDENTIFIER NOT NULL,
        ColumnName NVARCHAR(256) COLLATE Latin1_General_100_CI_AS NULL,
        LaneName NVARCHAR(256) COLLATE Latin1_General_100_CI_AS NULL,
        Done INT NULL,
        ChangedDate DATETIMEOFFSET NOT NULL,
        PRIMARY KEY CLUSTERED (WorkItemRevisionSK, ExtensionId)
    )

    INSERT  #ImpactedRev
    SELECT  r.WorkItemRevisionSK,
            wi.ExtensionId,
            wi.ColumnName,
            wi.LaneName,
            wi.Done,
            r.ChangedDate
    FROM    #ImpactedId i
    INNER LOOP JOIN AnalyticsInternal.tbl_WorkItemRevisionKanban wi
    ON      wi.PartitionId = @partitionId
            AND wi.System_Id = i.System_Id
            AND wi.System_Rev = i.System_Rev
    INNER LOOP JOIN
    (
        SELECT  PartitionId,
                WorkItemId,
                Revision,
                WorkItemRevisionSK,
                ChangedDate
        FROM    AnalyticsModel.tbl_WorkItemHistory WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev))
        UNION ALL
        SELECT  PartitionId,
                WorkItemId,
                Revision,
                WorkItemRevisionSK,
                ChangedDate
        FROM    AnalyticsModel.tbl_WorkItem WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev))
    ) r
    ON      r.PartitionId = wi.PartitionId
            AND r.WorkItemId = wi.System_Id
            AND r.Revision = wi.System_Rev
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #Src
    (
        WorkItemRevisionSK INT NOT NULL,
        BoardLocationSK INT NOT NULL,
        PRIMARY KEY CLUSTERED (WorkItemRevisionSK, BoardLocationSK)
    )

    INSERT  #Src (WorkItemRevisionSK, BoardLocationSK)
    SELECT  r.WorkItemRevisionSK,
            bl.BoardLocationSK
    FROM    #ImpactedRev r
    CROSS APPLY
            (
            SELECT  TOP 1 bl.BoardLocationSK
            FROM    AnalyticsModel.tbl_BoardLocation bl
            WHERE   @partitionId = bl.PartitionId
                    AND r.ExtensionId = bl.BoardExtensionId
                    AND r.ColumnName = bl.ColumnName
                    AND ((bl.IsColumnSplit = 0 AND bl.IsDone IS NULL) OR (bl.IsColumnSplit = 1 AND bl.IsDone = r.Done) OR (bl.IsColumnSplit = 1 AND bl.IsDone IS NULL AND r.Done IS NULL)) -- IGNORE .done if column not split
                    AND (bl.LaneName = r.LaneName OR (bl.IsDefaultLane = 1 AND r.LaneName IS NULL AND bl.IsVirtual = 0))
                    AND r.ChangedDate < bl.RevisedDate
            ORDER BY bl.ChangedDate ASC
            ) bl
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- tbl_WorkItemBoardLocation should be much larger, so force loop join using index
    DELETE  t
    FROM    #ImpactedRev AS r
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemBoardLocation AS t WITH (ROWLOCK)
    ON      t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK = r.WorkItemRevisionSK
    LEFT JOIN #Src AS s
    ON      s.WorkItemRevisionSK = t.WorkItemRevisionSK
            AND s.BoardLocationSK = t.BoardLocationSK
    WHERE   s.WorkItemRevisionSK IS NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DELETE  s
    FROM    #Src AS s
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemBoardLocation AS t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            AND t.BoardLocationSK = s.BoardLocationSK
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  AnalyticsModel.tbl_WorkItemBoardLocation
            (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            WorkItemRevisionSK,
            BoardLocationSK
            )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.WorkItemRevisionSK,
            s.BoardLocationSK
    FROM    #Src AS s

    SET @insertedCount = @@ROWCOUNT

    DROP TABLE #ImpactedBoards
    DROP TABLE #ImpactedId
    DROP TABLE #ImpactedRev
    DROP TABLE #Src

    RETURN 0
END

GO

