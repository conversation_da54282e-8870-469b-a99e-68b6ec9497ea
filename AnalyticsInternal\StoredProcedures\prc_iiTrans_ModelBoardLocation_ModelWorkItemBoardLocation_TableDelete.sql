/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FF3D78B9AAD360D49317095A374ED2249459C4C3
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelBoardLocation_ModelWorkItemBoardLocation_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    CREATE TABLE #UnknownBoardLocation (BoardLocationSK INT)

    INSERT  #UnknownBoardLocation
    SELECT  DISTINCT BoardLocationSK
    FROM    AnalyticsModel.tbl_WorkItemBoardLocation
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    #UnknownBoardLocation t
    JOIN    AnalyticsModel.tbl_BoardLocation s
    ON      s.PartitionId = @partitionId
            AND s.BoardLocationSK = t.BoardLocationSK
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  TOP (@batchSizeMax) AnalyticsModel.tbl_WorkItemBoardLocation
    FROM    AnalyticsModel.tbl_WorkItemBoardLocation t
    JOIN    #UnknownBoardLocation u
    ON      u.BoardLocationSK = t.BoardLocationSK
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    DROP TABLE #UnknownBoardLocation

    RETURN 0
END

GO

