/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 572C05163A90308DE860FB8FE392E46531A648CD
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelIteration_ModelTeamIteration_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    ;WITH Src AS
    (
        SELECT
            ti.PartitionId,
            ti.TeamGuid AS TeamSK,
            i.IterationSK
        FROM AnalyticsModel.tbl_Iteration i
        JOIN (
            SELECT t.*, i.IterationGuid.value('.[1]','uniqueidentifier') AS IterationId
            FROM AnalyticsStage.tbl_TeamSetting t
            CROSS APPLY Iterations.nodes('//Guid/text()') i(IterationGuid)
            ) ti
            ON ti.PartitionId = i.PartitionId AND ti.IterationId = i.IterationId
        WHERE i.PartitionId = @partitionId
            AND i.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    )
    MERGE TOP (@batchSizeMax) AnalyticsModel.tbl_TeamIteration AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId AND t.TeamSK = s.TeamSK AND t.IterationSK = s.IterationSK)
    WHEN NOT MATCHED BY TARGET
    THEN INSERT
        (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            TeamSK,
            IterationSK
        )
    VALUES
        (
        s.PartitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TeamSK,
        s.IterationSK
        )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = @@ROWCOUNT

    SET @complete = CASE WHEN @insertedCount < @batchSizeMax THEN 1 ELSE 0 END

    RETURN 0
END

GO

