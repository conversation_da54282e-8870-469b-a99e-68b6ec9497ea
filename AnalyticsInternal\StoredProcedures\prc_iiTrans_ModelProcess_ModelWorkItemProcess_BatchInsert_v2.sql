/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FA31694366B31B57FE0191AD1C58DB0985313AFB
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelProcess_ModelWorkItemProcess_BatchInsert_v2
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, NULL, 0) -- don't lower batch size for previous failures

    CREATE TABLE #ImpactedTeamFields
    (
        ProjectSK           UNIQUEIDENTIFIER NOT NULL,
        TeamFieldSK         INT NOT NULL,
        TeamSK              UNIQUEIDENTIFIER NOT NULL,
        WorkItemType        NVARCHAR(256) COLLATE DATABASE_DEFAULT NOT NULL,
        ProcessSK           INT NOT NULL,
        PRIMARY KEY (TeamFieldSK, WorkItemType, TeamSK)
    )

    IF (@triggerTableName = 'Model.Process')
    BEGIN
        INSERT  #ImpactedTeamFields
        SELECT  p.ProjectSK,
                ta.TeamFieldSK,
                ta.TeamSK,
                p.WorkItemType,
                p.ProcessSK
        FROM    AnalyticsModel.tbl_Process p
        JOIN    AnalyticsModel.tbl_TeamToTeamField ta
        ON      ta.PartitionId = p.PartitionId
                AND ta.TeamSK = p.TeamSK
        WHERE   p.PartitionId = @partitionId
                AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE IF (@triggerTableName = 'Model.TeamToTeamField')
    BEGIN
        INSERT  #ImpactedTeamFields
        SELECT  p.ProjectSK,
                ta.TeamFieldSK,
                ta.TeamSK,
                p.WorkItemType,
                p.ProcessSK
        FROM    AnalyticsModel.tbl_Process p
        JOIN    AnalyticsModel.tbl_TeamToTeamField ta
        ON      ta.PartitionId = p.PartitionId
                AND ta.TeamSK = p.TeamSK
        WHERE   p.PartitionId = @partitionId
                AND ta.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    CREATE TABLE #Src
    (
        WorkItemRevisionSK  INT NOT NULL,
        ProcessSK           INT NOT NULL
    )

    INSERT  #Src
    SELECT TOP (@batchSizeMax) WITH TIES
            WorkItemRevisionSK,
            ProcessSK
    FROM    #ImpactedTeamFields ia
    JOIN    (
            SELECT PartitionId, WorkItemRevisionSK, WorkItemType, TeamFieldSK FROM AnalyticsModel.tbl_WorkItem
            UNION ALL
            SELECT PartitionId, WorkItemRevisionSK, WorkItemType, TeamFieldSK FROM AnalyticsModel.tbl_WorkItemHistory
            ) m
    ON      ia.TeamFieldSK = m.TeamFieldSK
            AND ia.WorkItemType = m.WorkItemType
    WHERE   m.PartitionId = @partitionId
            AND m.WorkItemRevisionSK > ISNULL(@stateData, -1)
    ORDER BY m.WorkItemRevisionSK ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(WorkItemRevisionSK) FROM #Src)

    DELETE  s
    FROM    #Src s
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemProcess t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            AND t.ProcessSK = s.ProcessSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  AnalyticsModel.tbl_WorkItemProcess
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        WorkItemRevisionSK,
        ProcessSK
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.WorkItemRevisionSK,
            s.ProcessSK
    FROM    #Src AS s

    SET @insertedCount = @@ROWCOUNT

    DROP TABLE #Src
    DROP TABLE #ImpactedTeamFields

    RETURN 0
END

GO

