/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E535FB13A2475285553243E45E1C7AC452CF3537
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelProcess_ModelWorkItemProcess_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    CREATE TABLE #UnknownProcess (ProcessSK INT)

    INSERT  #UnknownProcess
    SELECT  DISTINCT ProcessSK
    FROM    AnalyticsModel.tbl_WorkItemProcess
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    #UnknownProcess t
    JOIN    AnalyticsModel.tbl_Process s
    ON      s.PartitionId = @partitionId
            AND s.ProcessSK = t.ProcessSK
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  TOP (@batchSizeMax) AnalyticsModel.tbl_WorkItemProcess
    FROM    AnalyticsModel.tbl_WorkItemProcess t
    JOIN    #UnknownProcess u
    ON      u.ProcessSK = t.ProcessSK
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    DROP TABLE #UnknownProcess

    RETURN 0
END

GO

