/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D53419379304E28C3E4B202056DDA21BE85BA2FF
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelProject_ModelTeamField_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 50000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart BIGINT = ISNULL(@stateData + 1, 0)

    CREATE TABLE #TeamFieldReferenceName
    (
        ProjectId               UNIQUEIDENTIFIER    NOT NULL,
        TeamFieldReferenceName  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL,
        FieldSK                 INT                 NOT NULL
        PRIMARY KEY (ProjectId)
    )

    INSERT  #TeamFieldReferenceName
    SELECT  ProjectId,
            TeamFieldReferenceName,
            FieldSK
    FROM    AnalyticsModel.tbl_Project p
    JOIN    AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = p.PartitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = p.TeamFieldSourceFieldName
    WHERE   p.PartitionId = @partitionId
            AND p.TeamFieldReferenceName <> 'System.AreaPath'
            AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@@ROWCOUNT = 0)
    BEGIN
        SET @complete = 1
        SET @insertedCount = 0
    END
    ELSE
    BEGIN
        CREATE TABLE #TeamField
        (
            WorkItemId              INT                 NOT NULL,
            ProjectId               UNIQUEIDENTIFIER    NOT NULL,
            TeamFieldReferenceName  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL,
            TeamFieldValue          NVARCHAR(4000)      COLLATE DATABASE_DEFAULT NULL
        )

        INSERT  #TeamField
        SELECT TOP (@batchSizeMax) WITH TIES r.System_Id,
                f.ProjectId,
                f.TeamFieldReferenceName,
                x.ValueString AS TeamFieldValue
        FROM    AnalyticsStage.tbl_WorkItemRevision r
        JOIN    #TeamFieldReferenceName f
        ON      f.ProjectId = r.System_ProjectGuid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
        ON      x.PartitionId = r.PartitionId
                AND x.System_Id = r.System_Id
                AND x.System_Rev = r.System_Rev
                AND x.FieldSK = f.FieldSK
        WHERE   r.PartitionId = @partitionId
                AND r.System_Id >= @workItemIdStart
        ORDER BY r.System_Id ASC
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(WorkItemId) FROM #TeamField)

        INSERT AnalyticsModel.tbl_TeamField
            (
                PartitionId,
                AnalyticsBatchId,
                AnalyticsCreatedDate,
                AnalyticsUpdatedDate,
                ProjectId,
                TeamFieldReferenceName,
                TeamFieldValue
            )
        SELECT  DISTINCT @partitionId,
                @batchId,
                @batchDt,
                @batchDt,
                s.ProjectId,
                s.TeamFieldReferenceName,
                s.TeamFieldValue
        FROM    #TeamField AS s
        LEFT JOIN AnalyticsModel.tbl_TeamField AS t
        ON      t.PartitionId = @partitionId
                AND t.ProjectId = s.ProjectId
                AND t.TeamFieldReferenceName = s.TeamFieldReferenceName
                AND t.TeamFieldValue = s.TeamFieldValue
        WHERE   t.PartitionId IS NULL
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT

        DROP TABLE #TeamField
    END

    DROP TABLE #TeamFieldReferenceName

    RETURN 0
END

GO

