/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 346D368608F73CFA2A270A917114BDF0A8EF50C4
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelProject_ModelWorkItemProcess_BatchReplace
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

    CREATE TABLE #ImpactedRev
    (
        TeamFieldSK         INT NULL,
        WorkItemId          INT NOT NULL,
        ProjectGuid         UNIQUEIDENTIFIER NULL,
        WorkItemType        NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        WorkItemRevisionSK  INT NOT NULL,
        PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
    )

    INSERT  #ImpactedRev
    SELECT  TOP (@batchSizeMax) WITH TIES
            r.TeamFieldSK,
            wi.System_Id,
            wi.System_ProjectGuid,
            wi.System_WorkItemType,
            r.WorkItemRevisionSK
    FROM    AnalyticsStage.tbl_WorkItemRevision wi
    INNER LOOP JOIN AnalyticsModel.tbl_Project p
    ON      wi.PartitionId = p.PartitionId
            AND wi.System_ProjectGuid = p.ProjectId
    INNER LOOP JOIN (
            SELECT PartitionId, WorkItemRevisionSK, WorkItemId, Revision, TeamFieldSK FROM AnalyticsModel.tbl_WorkItem WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev))
            UNION ALL
            SELECT PartitionId, WorkItemRevisionSK, WorkItemId, Revision, TeamFieldSK FROM AnalyticsModel.tbl_WorkItemHistory WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev))
            ) r
    ON      r.PartitionId = wi.PartitionId
            AND r.WorkItemId = wi.System_Id
            AND r.Revision = wi.System_Rev
    WHERE   wi.PartitionId = @partitionId
            AND wi.System_Id >= @workItemIdStart
            AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    ORDER BY wi.System_Id ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(WorkItemId) FROM #ImpactedRev)

    CREATE TABLE #Src
    (
        WorkItemRevisionSK  INT NOT NULL,
        ProcessSK           INT NOT NULL,
    )

    INSERT  #Src (WorkItemRevisionSK, ProcessSK)
    SELECT  wi.WorkItemRevisionSK,
            p.ProcessSK
    FROM    #ImpactedRev wi
    INNER LOOP JOIN AnalyticsModel.tbl_Process p
    ON      p.PartitionId = @partitionId
            AND p.ProjectSK = wi.ProjectGuid
            AND p.WorkItemType = wi.WorkItemType
    INNER LOOP JOIN AnalyticsModel.tbl_TeamToTeamField ta
    ON      ta.PartitionId = @partitionId
            AND ta.TeamFieldSK = wi.TeamFieldSK
            AND ta.TeamSK = p.TeamSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- tbl_WorkItemProcess should be much larger, so force loop join using index
    DELETE  t
    FROM    #ImpactedRev AS r
    LEFT JOIN #Src AS s
    ON      s.WorkItemRevisionSK = r.WorkItemRevisionSK
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemProcess AS t WITH (ROWLOCK)
    ON      r.WorkItemRevisionSK = t.WorkItemRevisionSK
            AND s.ProcessSK = t.ProcessSK
    WHERE   t.PartitionId = @partitionId
            AND s.WorkItemRevisionSK IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DELETE  s
    FROM    #Src s
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemProcess t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            AND t.ProcessSK = s.ProcessSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT AnalyticsModel.tbl_WorkItemProcess
        (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        WorkItemRevisionSK,
        ProcessSK
        )
    SELECT
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.WorkItemRevisionSK,
        s.ProcessSK
    FROM #Src AS s

    SET @insertedCount = @@ROWCOUNT

    DROP TABLE #ImpactedRev
    DROP TABLE #Src

    RETURN 0
END

GO

