/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 739917DBED85625410F55F1EB99C2383E85ED424
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelProject_ModelWorkItem_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    -- Backoff max batch size on failures
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    SET @updatedCount = 0
    DECLARE @workItemIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @workItemIdEnd BIGINT = @workItemIdStart + 100000
    DECLARE @workItemIdMax BIGINT = 0

    SELECT @workItemIdMax = ISNULL(MAX(WorkItemId), 0)
    FROM    (
            SELECT PartitionId, WorkItemId, Revision, ProjectSK, TeamFieldSK FROM AnalyticsModel.tbl_WorkItem
            UNION ALL
            SELECT PartitionId, WorkItemId, Revision, ProjectSK, TeamFieldSK FROM AnalyticsModel.tbl_WorkItemHistory
            ) wi
    WHERE  wi.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #WorkItemToFix
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        ProjectId UNIQUEIDENTIFIER NOT NULL,
        TeamFieldReferenceName NVARCHAR(256) COLLATE DATABASE_DEFAULT NOT NULL,
        TeamFieldSourceFieldName NVARCHAR(256) COLLATE DATABASE_DEFAULT
    )

    INSERT #WorkItemToFix
    SELECT TOP (@batchSizeMax) WITH TIES wi.WorkItemId,
            wi.Revision,
            p.ProjectId,
            ISNULL(p.TeamFieldReferenceName, 'System.AreaPath') AS TeamFieldReferenceName,
            p.TeamFieldSourceFieldName
    FROM    AnalyticsModel.tbl_Project p
    JOIN    (
            SELECT PartitionId, WorkItemId, Revision, ProjectSK, TeamFieldSK FROM AnalyticsModel.tbl_WorkItem
            UNION ALL
            SELECT PartitionId, WorkItemId, Revision, ProjectSK, TeamFieldSK FROM AnalyticsModel.tbl_WorkItemHistory
            ) wi
    ON      p.PartitionId = wi.PartitionId
            AND p.ProjectSK = wi.ProjectSK
    LEFT JOIN AnalyticsModel.tbl_TeamField tf -- team field from area
    ON      tf.PartitionId = p.PartitionId
            AND tf.TeamFieldSK = wi.TeamFieldSK
    WHERE   p.PartitionId = @partitionId
            AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND WorkItemId >= @workItemIdStart
            AND WorkItemId <= @workItemIdEnd
            AND (tf.TeamFieldReferenceName IS NULL OR tf.TeamFieldReferenceName <> ISNULL(p.TeamFieldReferenceName, 'System.AreaPath'))
    ORDER BY wi.WorkItemId ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    IF (@@ROWCOUNT < @batchSizeMax)
    BEGIN
        SET @complete = IIF(@workItemIdEnd >= @workItemIdMax, 1, 0)
        SET @endStateData = @workItemIdEnd
    END
    ELSE
    BEGIN
        SET @complete = 0
        SET @endStateData = (SELECT MAX(WorkItemId) FROM #WorkItemToFix)
    END

    UPDATE  r
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            TeamFieldSK = ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK)
    FROM    #WorkItemToFix br
    JOIN    AnalyticsModel.tbl_WorkItemHistory r
    ON      r.PartitionId = @partitionId
            AND r.WorkItemId = br.WorkItemId
            AND r.Revision = br.Revision
    LEFT JOIN AnalyticsModel.tbl_TeamField tf1 -- team field from area
    ON      tf1.PartitionId = @partitionId
            AND tf1.ProjectId = br.ProjectId
            AND tf1.TeamFieldReferenceName = br.TeamFieldReferenceName
            AND tf1.TeamFieldReferenceName = 'System.AreaPath'
            AND tf1.AreaId = r.AreaSK
    LEFT JOIN AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = @partitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = br.TeamFieldSourceFieldName
            AND br.TeamFieldReferenceName <> 'System.AreaPath'
    LEFT LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
    ON      x.PartitionId = @partitionId
            AND x.FieldSK = f.FieldSK
            AND x.System_Id = r.WorkItemId
            AND x.System_Rev = r.Revision
    LEFT JOIN AnalyticsModel.tbl_TeamField tf2 -- team field from custom fields
    ON      tf2.PartitionId = @partitionId
            AND tf2.ProjectId = br.ProjectId
            AND tf2.TeamFieldReferenceName = br.TeamFieldReferenceName
            AND tf2.TeamFieldReferenceName <> 'System.AreaPath'
            AND tf2.TeamFieldValue = x.ValueString
    WHERE   NOT EXISTS (SELECT r.TeamFieldSK INTERSECT SELECT ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK))
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    UPDATE  r
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            TeamFieldSK = ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK)
    FROM    #WorkItemToFix br
    JOIN    AnalyticsModel.tbl_WorkItem r
    ON      r.PartitionId = @partitionId
            AND r.WorkItemId = br.WorkItemId
            AND r.Revision = br.Revision
    LEFT JOIN AnalyticsModel.tbl_TeamField tf1 -- team field from area
    ON      tf1.PartitionId = @partitionId
            AND tf1.ProjectId = br.ProjectId
            AND tf1.TeamFieldReferenceName = br.TeamFieldReferenceName
            AND tf1.TeamFieldReferenceName = 'System.AreaPath'
            AND tf1.AreaId = r.AreaSK
    LEFT JOIN AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = @partitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = br.TeamFieldSourceFieldName
            AND br.TeamFieldReferenceName <> 'System.AreaPath'
    LEFT LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
    ON      x.PartitionId = @partitionId
            AND x.FieldSK = f.FieldSK
            AND x.System_Id = r.WorkItemId
            AND x.System_Rev = r.Revision
    LEFT JOIN AnalyticsModel.tbl_TeamField tf2 -- team field from custom fields
    ON      tf2.PartitionId = @partitionId
            AND tf2.ProjectId = br.ProjectId
            AND tf2.TeamFieldReferenceName = br.TeamFieldReferenceName
            AND tf2.TeamFieldReferenceName <> 'System.AreaPath'
            AND tf2.TeamFieldValue = x.ValueString
    WHERE   NOT EXISTS (SELECT r.TeamFieldSK INTERSECT SELECT ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK))
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    DROP TABLE #WorkItemToFix

    RETURN 0
END

GO

