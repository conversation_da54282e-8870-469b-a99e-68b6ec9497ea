/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B2B801317B3F2028E5467CFC1B0088F90AE63A9F
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTag_ModelWorkItemTag_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    CREATE TABLE #UnknownTag (TagSK INT)

    INSERT  #UnknownTag
    SELECT  DISTINCT TagSK
    FROM    AnalyticsModel.tbl_WorkItemTag
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    #UnknownTag t
    JOIN    AnalyticsModel.tbl_Tag s
    ON      s.PartitionId = @partitionId
            AND s.TagSK = t.TagSK
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_WorkItemTag t
    JOIN    #UnknownTag u
    ON      u.TagSK = t.TagSK
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    DROP TABLE #UnknownTag

    RETURN 0
END

GO

