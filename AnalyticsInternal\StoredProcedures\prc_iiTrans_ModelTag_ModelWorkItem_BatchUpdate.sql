/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 03E6D2D5484E4798F6CD8CF072B9C86408029C64
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTag_ModelWorkItem_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 50000)
    DECLARE @batchSizeLarge INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeLarge'), 100)
    SET @endStateData = ISNULL(@stateData, 0)

    CREATE TABLE #UpdatedWIWithTags
    (
        WorkItemRevisionSK          INT                                         NOT NULL,
        TagName                     NVARCHAR(400)  COLLATE DATABASE_DEFAULT     NOT NULL
    )

    CREATE TABLE #RevsWithConcatTags
    (
        WorkItemRevisionSK          INT                                         NOT NULL PRIMARY KEY,
        TagNames                    NVARCHAR(1024)  COLLATE DATABASE_DEFAULT    NOT NULL
    )

    CREATE TABLE #WorkItemRevsForCurrentBatch
    (
        WorkItemRevisionSK          INT             NOT NULL
    )

    IF (@triggerBatchIdStart > 1)
    BEGIN
        INSERT  #WorkItemRevsForCurrentBatch (WorkItemRevisionSK)
        SELECT  TOP (@batchSizeMax) witag.WorkItemRevisionSK
        FROM    AnalyticsModel.tbl_Tag m WITH (INDEX (IX_tbl_Tag_AnalyticsBatchId))
        JOIN    AnalyticsModel.tbl_WorkItemTag witag
        ON      witag.PartitionId = @partitionId
                AND witag.TagSK = m.TagSK
                AND witag.WorkItemRevisionSK > @endStateData
        WHERE   m.PartitionId = @partitionId
                AND m.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        ORDER BY witag.WorkItemRevisionSK ASC
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        INSERT  #WorkItemRevsForCurrentBatch (WorkItemRevisionSK)
        SELECT  TOP (@batchSizeMax) witag.WorkItemRevisionSK
        FROM    AnalyticsModel.tbl_WorkItemTag witag WITH (INDEX (PK_tbl_WorkItemTag))
        WHERE   witag.PartitionId = @partitionId
                AND witag.WorkItemRevisionSK > @endStateData
        ORDER BY witag.WorkItemRevisionSK ASC
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(WorkItemRevisionSK) FROM #WorkItemRevsForCurrentBatch)

    ;WITH Src AS
    (
        SELECT  WorkItemRevisionSK, TagSK
        FROM    AnalyticsModel.tbl_WorkItemTag
        WHERE   PartitionId = @partitionId
                AND WorkItemRevisionSK IN (SELECT WorkItemRevisionSK FROM #WorkItemRevsForCurrentBatch)
    )
    INSERT  #UpdatedWIWithTags(WorkItemRevisionSK, TagName)
    SELECT  WorkItemRevisionSK,
            TagName
    FROM    Src s
    JOIN    AnalyticsModel.tbl_Tag m1
    ON      m1.PartitionId = @partitionId
            AND m1.TagSK = s.TagSK
            AND m1.TagName IS NOT NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #RevsWithConcatTags (WorkItemRevisionSK, TagNames)
    SELECT  WorkItemRevisionSK,
            STUFF(( SELECT  '; ' + CAST(TagName AS NVARCHAR(400))
                    FROM    #UpdatedWIWithTags
                    WHERE   WorkItemRevisionSK = r.WorkItemRevisionSK
                    ORDER BY TagName
                    FOR XML PATH(''), TYPE).value('(./text())[1]','NVARCHAR(1026)')
                    ,1, 2, '') TagNames
    FROM    #UpdatedWIWithTags r
    GROUP BY WorkItemRevisionSK

    DECLARE @updatingCount INT = (SELECT COUNT(*) FROM #RevsWithConcatTags)

    ---------------------------------------------------------------------------------------
    ------------update WorkItemHistory
    ---------------------------------------------------------------------------------------
    IF (@updatingCount > @batchSizeLarge)
    BEGIN
        UPDATE  t
        SET     AnalyticsUpdatedDate        = @batchDt,
                AnalyticsBatchId            = @batchId,
                t.TagNames                  = tags.TagNames
        FROM    #WorkItemRevsForCurrentBatch s
        LEFT JOIN #RevsWithConcatTags tags
        ON      tags.WorkItemRevisionSK = s.WorkItemRevisionSK
        INNER HASH JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force hash join for reprocess
        ON      @partitionId = t.PartitionId
                AND s.WorkItemRevisionSK = t.WorkItemRevisionSK
        WHERE   NOT EXISTS
                (
                    SELECT  t.TagNames
                    INTERSECT
                    SELECT  tags.TagNames
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE
    BEGIN
        UPDATE  t
        SET     AnalyticsUpdatedDate        = @batchDt,
                AnalyticsBatchId            = @batchId,
                t.TagNames                  = tags.TagNames
        FROM    #WorkItemRevsForCurrentBatch s
        LEFT JOIN #RevsWithConcatTags tags
        ON      tags.WorkItemRevisionSK = s.WorkItemRevisionSK
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemRevisionSK))
        ON      @partitionId = t.PartitionId
                AND s.WorkItemRevisionSK = t.WorkItemRevisionSK
        WHERE   NOT EXISTS
                (
                    SELECT  t.TagNames
                    INTERSECT
                    SELECT  tags.TagNames
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END

    SET @updatedCount = @@ROWCOUNT

    ---------------------------------------------------------------------------------------
    ------------update WorkItem
    ---------------------------------------------------------------------------------------
    IF (@updatingCount > @batchSizeLarge)
    BEGIN
        UPDATE  t
        SET     AnalyticsUpdatedDate        = @batchDt,
                AnalyticsBatchId            = @batchId,
                t.TagNames                  = tags.TagNames
        FROM    #WorkItemRevsForCurrentBatch s
        LEFT JOIN #RevsWithConcatTags tags
        ON      tags.WorkItemRevisionSK = s.WorkItemRevisionSK
        INNER HASH JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem)) -- force hash join for reprocess
        ON      @partitionId = t.PartitionId
                AND s.WorkItemRevisionSK = t.WorkItemRevisionSK
        WHERE   NOT EXISTS
                (
                    SELECT  t.TagNames
                    INTERSECT
                    SELECT  tags.TagNames
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE
    BEGIN
        UPDATE  t
        SET     AnalyticsUpdatedDate        = @batchDt,
                AnalyticsBatchId            = @batchId,
                t.TagNames                  = tags.TagNames
        FROM    #WorkItemRevsForCurrentBatch s
        LEFT JOIN #RevsWithConcatTags tags
        ON      tags.WorkItemRevisionSK = s.WorkItemRevisionSK
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (IX_tbl_WorkItem_WorkItemRevisionSK))
        ON      @partitionId = t.PartitionId
                AND s.WorkItemRevisionSK = t.WorkItemRevisionSK
        WHERE   NOT EXISTS
                (
                    SELECT  t.TagNames
                    INTERSECT
                    SELECT  tags.TagNames
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END

    SET @updatedCount += @@ROWCOUNT

    DROP TABLE #UpdatedWIWithTags
    DROP TABLE #RevsWithConcatTags
    DROP TABLE #WorkItemRevsForCurrentBatch

    RETURN 0
END

GO

