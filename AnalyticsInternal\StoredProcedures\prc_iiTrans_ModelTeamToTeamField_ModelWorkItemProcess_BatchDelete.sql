/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 466758D5C82F0B3621298F65682D8811BA95060C
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTeamToTeamField_ModelWorkItemProcess_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, NULL, 0) -- don't lower batch size for previous failures

    CREATE TABLE #ImpactedTeamFields
    (
        ProjectSK           UNIQUEIDENTIFIER NOT NULL,
        TeamFieldSK         INT NOT NULL,
        TeamSK              UNIQUEIDENTIFIER NOT NULL,
        WorkItemType        NVARCHAR(256) COLLATE DATABASE_DEFAULT NOT NULL,
        ProcessSK           INT NOT NULL,
        PRIMARY KEY (TeamFieldSK, WorkItemType, TeamSK)
    )

    INSERT  #ImpactedTeamFields
    SELECT  DISTINCT p.ProjectSK,
            ta.TeamFieldSK,
            ta.TeamSK,
            p.WorkItemType,
            p.ProcessSK
    FROM    AnalyticsModel.tbl_Process p
    JOIN    AnalyticsModel.tbl_TeamToTeamField_Deleted ta
    ON      ta.PartitionId = p.PartitionId
            AND ta.TeamSK = p.TeamSK
    WHERE   p.PartitionId = @partitionId
            AND ta.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  ia
    FROM    #ImpactedTeamFields ia
    JOIN    AnalyticsModel.tbl_TeamToTeamField ta
    ON      ta.PartitionId = @partitionId
            AND ta.TeamSK = ia.TeamSK
            AND ta.TeamFieldSK = ia.TeamFieldSK

    CREATE TABLE #Src
    (
        WorkItemRevisionSK  INT NOT NULL,
        ProcessSK           INT NOT NULL
    )

    INSERT  #Src
    SELECT TOP (@batchSizeMax) WITH TIES
            WorkItemRevisionSK,
            ProcessSK
    FROM    #ImpactedTeamFields ia
    JOIN    (
            SELECT PartitionId, WorkItemRevisionSK, WorkItemType, TeamFieldSK FROM AnalyticsModel.tbl_WorkItem
            UNION ALL
            SELECT PartitionId, WorkItemRevisionSK, WorkItemType, TeamFieldSK FROM AnalyticsModel.tbl_WorkItemHistory
            ) m
    ON      ia.TeamFieldSK = m.TeamFieldSK
            AND ia.WorkItemType = m.WorkItemType
    WHERE   m.PartitionId = @partitionId
            AND m.WorkItemRevisionSK > ISNULL(@stateData, -1)
    ORDER BY m.WorkItemRevisionSK ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(WorkItemRevisionSK) FROM #Src)

    DELETE  t
    FROM    #Src s
    JOIN    AnalyticsModel.tbl_WorkItemProcess t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            AND t.ProcessSK = s.ProcessSK

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #Src
    DROP TABLE #ImpactedTeamFields

    RETURN 0
END

GO

