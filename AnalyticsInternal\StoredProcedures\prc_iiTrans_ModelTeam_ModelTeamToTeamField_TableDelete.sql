/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 48B4946BEFDD129CE487A9A1F90C2E7BAB48377B
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTeam_ModelTeamToTeamField_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @deleted TABLE
    (
        TeamSK UNIQUEIDENTIFIER,
        TeamFieldSK INT
    )

    DELETE  TOP (@batchSizeMax) t
    OUTPUT  DELETED.TeamSK, DELETED.TeamFieldSK INTO @deleted
    FROM    AnalyticsModel.tbl_TeamToTeamField t
    LEFT JOIN AnalyticsModel.tbl_Team tf
    ON      tf.PartitionId = t.PartitionId
            AND tf.TeamSK = t.TeamSK
    WHERE   t.PartitionId = @partitionId
            AND tf.TeamSK IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    INSERT AnalyticsModel.tbl_TeamToTeamField_Deleted
    (
        PartitionId,
        AnalyticsBatchIdDeleted,
        AnalyticsDeletedDate,
        TeamSK,
        TeamFieldSK
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            TeamSK,
            TeamFieldSK
    FROM    @deleted

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

