/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 74FB6DE301C8378C862C7FA775D5A2E37FE27498
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestConfiguration_ModelTestPoint_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #NewTestConfigurations
    (
        TestConfigurationId INT NOT NULL PRIMARY KEY,
        TestConfigurationSK INT NOT NULL
    )

    INSERT #NewTestConfigurations
    (
        TestConfigurationId,
        TestConfigurationSK
    )
    SELECT TOP (@batchSizeMax)
           mtc.TestConfigurationId,
           mtc.TestConfigurationSK
    FROM   AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_AnalyticsBatchId), FORCESEEK)
    WHERE  PartitionId = @partitionId
           AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
           AND TestConfigurationId > ISNULL(@stateData, 0)
    ORDER BY TestConfigurationId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT TOP 1 TestConfigurationId FROM #NewTestConfigurations ORDER BY TestConfigurationId DESC)

    UPDATE t
    SET    AnalyticsUpdatedDate = @batchDt,
           AnalyticsBatchId     = @batchId,
           TestConfigurationSK  = s.TestConfigurationSK
    FROM   #NewTestConfigurations s
    JOIN   AnalyticsModel.tbl_TestPoint t
    ON     t.PartitionId = @partitionId
           AND t.TestConfigurationId = s.TestConfigurationId
    WHERE  NOT EXISTS
           (
                SELECT
                t.TestConfigurationSK
                INTERSECT
                SELECT
                s.TestConfigurationSK
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT

    DROP TABLE #NewTestConfigurations

    RETURN 0
END

GO

