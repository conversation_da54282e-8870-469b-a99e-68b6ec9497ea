/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 3499A9F93B025A0998A809D57AA3BA78B585D356
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestRun_ModelReleaseEnvironment_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedReleaseEnvironment (ReleaseEnvironmentSK INT)

    INSERT  #DeletedReleaseEnvironment
    SELECT  TOP (@batchSizeMax) b.ReleaseEnvironmentSK
    FROM    AnalyticsModel.tbl_ReleaseEnvironment b
    LEFT HASH JOIN   AnalyticsModel.tbl_TestRun r
    ON      b.PartitionId = r.PartitionId
            AND b.ReleaseEnvironmentSK = r.ReleaseEnvironmentSK
    WHERE   b.PartitionId = @partitionId
            AND r.PartitionId IS NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)

    DELETE  t
    FROM    AnalyticsModel.tbl_ReleaseEnvironment t
    JOIN    #DeletedReleaseEnvironment d
    ON      d.ReleaseEnvironmentSK = t.ReleaseEnvironmentSK
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedReleaseEnvironment

    RETURN 0
END

GO

