/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D09AF702DC2FB68A8EBDF65D97AE3A55CF9D0D4E
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestRun_ModelRelease_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedRelease (ReleaseSK INT)

    INSERT  #DeletedRelease
    SELECT  TOP (@batchSizeMax) b.ReleaseSK
    FROM    AnalyticsModel.tbl_Release b
    LEFT HASH JOIN   AnalyticsModel.tbl_TestRun r
    ON      b.PartitionId = r.PartitionId
            AND b.ReleaseSK = r.ReleaseSK
    WHERE   b.PartitionId = @partitionId
            AND r.PartitionId IS NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)

    DELETE  t
    FROM    AnalyticsModel.tbl_Release t
    JOIN    #DeletedRelease d
    ON      d.ReleaseSK = t.ReleaseSK
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedRelease

    RETURN 0
END

GO

