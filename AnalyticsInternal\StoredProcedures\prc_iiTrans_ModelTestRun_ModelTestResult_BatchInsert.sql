/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D5969F90C378A618E50D599873C76AC63863E2E1
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestRun_ModelTestResult_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    --Get the retention date
    DECLARE @retentionDays INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'Target.RetentionDays'), 30)
    DECLARE @retentionDate DATE = CAST(DATEADD(day, 0 - @retentionDays, GETUTCDATE() AT TIME ZONE @timeZone) AS DATE)

    CREATE TABLE #ImpactedRuns
    (
        TestRunId INT NOT NULL,
        DataSourceId INT NOT NULL
    )

    IF @stateData IS NULL
    BEGIN
        INSERT  #ImpactedRuns
        SELECT  TOP (@batchSizeMax) WITH TIES
                TestRunId,
                DataSourceId
        FROM    AnalyticsModel.tbl_TestRun WITH (INDEX (IX_tbl_TestRun_AnalyticsBatchId))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY TestRunId, DataSourceId
        ORDER BY TestRunId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        -- We have more than one batch => switching to CCI that is faster for big scans
        INSERT  #ImpactedRuns
        SELECT  TOP (@batchSizeMax) WITH TIES
                TestRunId,
                DataSourceId
        FROM    AnalyticsModel.tbl_TestRun WITH (INDEX (CL_AnalyticsModel_tbl_TestRun))
        WHERE   PartitionId = @partitionId
                AND TestRunId > @stateData
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY TestRunId, DataSourceId
        ORDER BY TestRunId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(TestRunId) FROM #ImpactedRuns)

    INSERT  AnalyticsModel.tbl_TestResult
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        TestRunId,
        DataSourceId,
        TestResultId,
        ProjectSK,
        TestRunSK,
        TestSK,
        ReleaseSK,
        ReleaseEnvironmentSK,
        BuildSK,
        ReleasePipelineSK,
        ReleaseStageSK,
        BuildPipelineSK,
        BranchSK,
        Outcome,
        StartedDate,
        StartedDateSK,
        CompletedDate,
        CompletedDateSK,
        DurationSeconds,
        Workflow,
        TestRunType
    )
    SELECT  @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            TestRunId,
            DataSourceId,
            TestResultId,
            ProjectGuid,
            TestRunSK,
            TestSK,
            ReleaseSK,
            ReleaseEnvironmentSK,
            BuildSK,
            ReleasePipelineSK,
            ReleaseStageSK,
            BuildPipelineSK,
            BranchSK,
            Outcome,
            DateStarted,
            --AnalyticsInternal.func_GenDateSK(DateStarted) AS DateStartedSK,
            IIF(YEAR(DateStarted) >= 9999, NULL, YEAR(DateStarted) * 10000 + MONTH(DateStarted) * 100 + DAY(DateStarted)) AS DateStartedSK,
            DateCompleted,
            --AnalyticsInternal.func_GenDateSK(DateCompleted) AS DateCompletedSK,
            IIF(YEAR(DateCompleted) >= 9999, NULL, YEAR(DateCompleted) * 10000 + MONTH(DateCompleted) * 100 + DAY(DateCompleted)) AS DateCompletedSK,
            DurationSeconds,
            Workflow,
            TestRunType
    FROM
    (
        SELECT  s.TestRunId,
                s.DataSourceId,
                s.TestResultId,
                s.ProjectGuid,
                mtr.TestRunSK,
                t.TestSK,
                mtr.ReleaseSK,
                mtr.ReleaseEnvironmentSK,
                mtr.BuildSK,
                mtr.ReleasePipelineSK,
                mtr.ReleaseStageSK,
                mtr.BuildPipelineSK,
                mtr.BranchSK,
                s.Outcome AS Outcome,
                s.DateStarted AT TIME ZONE @timeZone AS DateStarted,
                s.DateCompleted AT TIME ZONE @timeZone AS DateCompleted,
                DATEDIFF_BIG(millisecond, s.DateStarted, s.DateCompleted) / 1000.0 AS DurationSeconds,
                mtr.Workflow,
                mtr.TestRunType
        FROM    #ImpactedRuns ir
        JOIN    AnalyticsModel.tbl_TestRun mtr
        ON      mtr.PartitionId = @partitionId
                AND mtr.TestRunId = ir.TestRunId
                AND mtr.DataSourceId = ir.DataSourceId
        INNER LOOP JOIN AnalyticsStage.tbl_TestResult s WITH (INDEX (CI_tbl_TestResult))
        ON      ir.TestRunId = s.TestRunId
                AND ir.DataSourceId = s.DataSourceId
                AND s.AnalyticsBatchId <= @triggerBatchIdEnd -- limit so that StageTestResult_ModelTest has a chance to create placeholders
        LEFT JOIN AnalyticsModel.tbl_Test t WITH (INDEX (UQ_tbl_Test_TestCaseReferenceId))
        ON      t.PartitionId = @partitionId
                AND t.TestCaseReferenceId = s.TestCaseReferenceId
                AND t.DataSourceId = s.DataSourceId
        LEFT LOOP JOIN AnalyticsModel.tbl_TestResult m WITH (INDEX (IX_tbl_TestResult_TestResultId))
        ON      m.PartitionId = @partitionId
                AND m.TestRunId = s.TestRunId
                AND m.TestResultId = s.TestResultId
                AND m.DataSourceId = s.DataSourceId
        WHERE   s.PartitionId = @partitionId
                AND s.DateCompleted >= @retentionDate
                AND s.DateCompleted IS NOT NULL -- TestResult must be completed
                AND s.DateCompleted <= DATEADD(DAY, 1, SYSUTCDATETIME()) -- Ignore Test results from distant future
                AND m.TestResultId IS NULL
    ) core
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    DROP TABLE #ImpactedRuns

    RETURN 0
END

GO

