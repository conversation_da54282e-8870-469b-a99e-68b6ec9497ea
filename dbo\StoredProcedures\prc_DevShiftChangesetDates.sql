CREATE PROCEDURE [dbo].[prc_DevShiftChangesetDates]
    @partitionId INT,
    @changesetIds NVARCHAR(MAX) = NULL, -- comma-separated list, NULL for all
    @shiftDays INT = 0,
    @shiftHours INT = 0,
    @shiftMinutes INT = 0,
    @dryRun BIT = 1 -- 1 for preview, 0 for actual execution
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT ON

    DECLARE @timeShift DATETIME2 = DATEADD(MINUTE, @shiftMinutes, 
                                  DATEADD(HOUR, @shiftHours, 
                                  DATEADD(DAY, @shiftDays, '1900-01-01')))
    DECLARE @shiftInterval BIGINT = DATEDIFF_BIG(SECOND, '1900-01-01', @timeShift)

    -- Create temp table for changeset IDs
    CREATE TABLE #ChangesetIds (ChangeSetId INT PRIMARY KEY)
    
    IF @changesetIds IS NULL
    BEGIN
        INSERT #ChangesetIds
        SELECT DISTINCT ChangeSetId FROM dbo.tbl_ChangeSet 
        WHERE PartitionId = @partitionId
    END
    ELSE
    BEGIN
        INSERT #ChangesetIds
        SELECT CAST(value AS INT) FROM STRING_SPLIT(@changesetIds, ',')
        WHERE ISNUMERIC(value) = 1
    END

    IF @dryRun = 1
    BEGIN
        -- Preview changes
        SELECT 
            c.ChangeSetId,
            c.CreationDate AS CurrentDate,
            DATEADD(SECOND, @shiftInterval, c.CreationDate) AS NewDate,
            c.Comment
        FROM dbo.tbl_ChangeSet c
        JOIN #ChangesetIds ci ON c.ChangeSetId = ci.ChangeSetId
        WHERE c.PartitionId = @partitionId
        ORDER BY c.ChangeSetId

        SELECT 'Total changesets to update: ' + CAST(COUNT(*) AS NVARCHAR(10)) AS Summary
        FROM dbo.tbl_ChangeSet c
        JOIN #ChangesetIds ci ON c.ChangeSetId = ci.ChangeSetId
        WHERE c.PartitionId = @partitionId

        RETURN 0
    END

    BEGIN TRANSACTION

    -- Update changeset creation dates
    UPDATE c
    SET CreationDate = DATEADD(SECOND, @shiftInterval, CreationDate)
    FROM dbo.tbl_ChangeSet c
    JOIN #ChangesetIds ci ON c.ChangeSetId = ci.ChangeSetId
    WHERE c.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @updatedCount INT = @@ROWCOUNT

    COMMIT TRANSACTION

    SELECT 
        'Date shift completed successfully' AS Result,
        @updatedCount AS ChangesetsUpdated
END