CREATE PROCEDURE [dbo].[prc_DevShiftWorkItemDates]
    @partitionId INT,
    @workItemIds NVARCHAR(MAX) = NULL, -- comma-separated list, NULL for all
    @shiftDays INT = 0,
    @shiftHours INT = 0,
    @shiftMinutes INT = 0,
    @dryRun BIT = 1 -- 1 for preview, 0 for actual execution
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT ON

    DECLARE @timeShift DATETIME2 = DATEADD(MINUTE, @shiftMinutes, 
                                  DATEADD(HOUR, @shiftHours, 
                                  DATEADD(DAY, @shiftDays, '1900-01-01')))
    DECLARE @shiftInterval BIGINT = DATEDIFF_BIG(SECOND, '1900-01-01', @timeShift)

    -- Create temp table for work item IDs
    CREATE TABLE #WorkItemIds (WorkItemId INT PRIMARY KEY)
    
    IF @workItemIds IS NULL
    BEGIN
        INSERT #WorkItemIds
        SELECT DISTINCT Id FROM dbo.tbl_WorkItemCoreLatest 
        WHERE PartitionId = @partitionId
    END
    ELSE
    BEGIN
        INSERT #WorkItemIds
        SELECT value FROM STRING_SPLIT(@workItemIds, ',')
        WHERE ISNUMERIC(value) = 1
    END

    IF @dryRun = 1
    BEGIN
        -- Preview changes
        SELECT 'WorkItemCoreLatest' AS TableName, COUNT(*) AS RecordsToUpdate
        FROM dbo.tbl_WorkItemCoreLatest w
        JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
        WHERE w.PartitionId = @partitionId

        UNION ALL

        SELECT 'WorkItemCoreWere' AS TableName, COUNT(*)
        FROM dbo.tbl_WorkItemCoreWere w
        JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
        WHERE w.PartitionId = @partitionId

        UNION ALL

        SELECT 'WorkItemCustomWere' AS TableName, COUNT(*)
        FROM dbo.tbl_WorkItemCustomWere c
        JOIN #WorkItemIds wi ON c.ID = wi.WorkItemId
        WHERE c.PartitionId = @partitionId

        UNION ALL

        SELECT 'WorkItemFiles' AS TableName, COUNT(*)
        FROM dbo.WorkItemFiles f
        JOIN #WorkItemIds wi ON f.ID = wi.WorkItemId
        WHERE f.PartitionId = @partitionId

        UNION ALL

        SELECT 'LinksAre' AS TableName, COUNT(*)
        FROM dbo.LinksAre l
        JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
        WHERE l.PartitionId = @partitionId

        UNION ALL

        SELECT 'LinksWere' AS TableName, COUNT(*)
        FROM dbo.LinksWere l
        JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
        WHERE l.PartitionId = @partitionId

        UNION ALL

        SELECT 'WorkItemLinksDestroyed' AS TableName, COUNT(*)
        FROM dbo.WorkItemLinksDestroyed l
        JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
        WHERE l.PartitionId = @partitionId

        RETURN 0
    END

    BEGIN TRANSACTION

    -- Update current work items
    UPDATE w
    SET CreatedDate = DATEADD(SECOND, @shiftInterval, CreatedDate),
        ChangedDate = DATEADD(SECOND, @shiftInterval, ChangedDate),
        AuthorizedDate = CASE WHEN AuthorizedDate IS NOT NULL 
                             THEN DATEADD(SECOND, @shiftInterval, AuthorizedDate) 
                             ELSE NULL END,
        RevisedDate = CASE WHEN RevisedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RevisedDate) 
                          ELSE RevisedDate END
    FROM dbo.tbl_WorkItemCoreLatest w
    JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
    WHERE w.PartitionId = @partitionId

    -- Update work item history
    UPDATE w
    SET CreatedDate = DATEADD(SECOND, @shiftInterval, CreatedDate),
        ChangedDate = DATEADD(SECOND, @shiftInterval, ChangedDate),
        AuthorizedDate = CASE WHEN AuthorizedDate IS NOT NULL 
                             THEN DATEADD(SECOND, @shiftInterval, AuthorizedDate) 
                             ELSE NULL END,
        RevisedDate = CASE WHEN RevisedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RevisedDate) 
                          ELSE RevisedDate END
    FROM dbo.tbl_WorkItemCoreWere w
    JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
    WHERE w.PartitionId = @partitionId

    -- Update custom field history
    UPDATE c
    SET AddedDate = DATEADD(SECOND, @shiftInterval, AddedDate),
        RemovedDate = CASE WHEN RemovedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RemovedDate) 
                          ELSE RemovedDate END
    FROM dbo.tbl_WorkItemCustomWere c
    JOIN #WorkItemIds wi ON c.ID = wi.WorkItemId
    WHERE c.PartitionId = @partitionId

    -- Update file attachments
    UPDATE f
    SET AddedDate = DATEADD(SECOND, @shiftInterval, AddedDate),
        RemovedDate = CASE WHEN RemovedDate < '9999-01-01'
                          THEN DATEADD(SECOND, @shiftInterval, RemovedDate)
                          ELSE RemovedDate END
    FROM dbo.WorkItemFiles f
    JOIN #WorkItemIds wi ON f.ID = wi.WorkItemId
    WHERE f.PartitionId = @partitionId

    -- Update current work item links
    UPDATE l
    SET [Created Date] = DATEADD(SECOND, @shiftInterval, [Created Date])
    FROM dbo.LinksAre l
    JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
    WHERE l.PartitionId = @partitionId

    -- Update work item link history
    UPDATE l
    SET [Created Date] = DATEADD(SECOND, @shiftInterval, [Created Date]),
        [Removed Date] = CASE WHEN [Removed Date] < '9999-01-01'
                             THEN DATEADD(SECOND, @shiftInterval, [Removed Date])
                             ELSE [Removed Date] END
    FROM dbo.LinksWere l
    JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
    WHERE l.PartitionId = @partitionId

    -- Update destroyed work item links
    UPDATE l
    SET ChangedDate = DATEADD(SECOND, @shiftInterval, ChangedDate)
    FROM dbo.WorkItemLinksDestroyed l
    JOIN #WorkItemIds wi ON (l.SourceID = wi.WorkItemId OR l.TargetID = wi.WorkItemId)
    WHERE l.PartitionId = @partitionId

    COMMIT TRANSACTION

    SELECT 'Date shift completed successfully' AS Result
END