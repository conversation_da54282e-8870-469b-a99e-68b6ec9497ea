{"format": 1, "restore": {"s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj": {}}, "projects": {"s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj": {"version": "1.0.0", "restore": {"projectUniqueName": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj", "projectName": "DatabaseProjectAzureDevOps_WebAggrAI", "projectPath": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\obj\\", "projectStyle": "PackageReference", "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1701", "NU5128"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "imports": ["netcoreapp1.0", "netcoreapp1.1", "netcoreapp2.0", "netcoreapp2.1", "netcoreapp2.2", "netcoreapp3.0", "netcoreapp3.1", "net5.0", "net6.0", "net7.0", "net8.0", "net9.0", "netstandard1.0", "netstandard1.1", "netstandard1.2", "netstandard1.3", "netstandard1.4", "netstandard1.5", "netstandard1.6", "netstandard2.0", "netstandard2.1", "net20", "net30", "net35", "net40", "net45", "net451", "net452", "net46", "net461", "net462", "net47", "net471", "net472", "net48", "net481", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}