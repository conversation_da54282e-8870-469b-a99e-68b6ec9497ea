{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net20": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net20/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net20.targets": {}}}}}, "libraries": {"Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"sha512": "vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net20/1.0.3": {"sha512": "mvyTeaG1dVyCx24hjVnqk1YvR4fwS1fHxt1tdnzWMGCHc09mvNVGF+8VAobWSDi+iDz6fgJ7jE5d/mWap4PYoA==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net20/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v2.0/Accessibility.dll", "build/.NETFramework/v2.0/AspNetMMCExt.dll", "build/.NETFramework/v2.0/CustomMarshalers.dll", "build/.NETFramework/v2.0/IEExecRemote.dll", "build/.NETFramework/v2.0/IEHost.dll", "build/.NETFramework/v2.0/IIEHost.dll", "build/.NETFramework/v2.0/ISymWrapper.dll", "build/.NETFramework/v2.0/Microsoft.Build.Engine.dll", "build/.NETFramework/v2.0/Microsoft.Build.Framework.dll", "build/.NETFramework/v2.0/Microsoft.Build.Tasks.dll", "build/.NETFramework/v2.0/Microsoft.Build.Utilities.dll", "build/.NETFramework/v2.0/Microsoft.JScript.dll", "build/.NETFramework/v2.0/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v2.0/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v2.0/Microsoft.VisualBasic.Vsa.dll", "build/.NETFramework/v2.0/Microsoft.VisualBasic.dll", "build/.NETFramework/v2.0/Microsoft.VisualC.dll", "build/.NETFramework/v2.0/Microsoft.Vsa.Vb.CodeDOMProcessor.dll", "build/.NETFramework/v2.0/Microsoft.Vsa.dll", "build/.NETFramework/v2.0/Microsoft_VsaVb.dll", "build/.NETFramework/v2.0/RedistList/FrameworkList.xml", "build/.NETFramework/v2.0/SubsetList/Client.xml", "build/.NETFramework/v2.0/System.Configuration.Install.dll", "build/.NETFramework/v2.0/System.Configuration.dll", "build/.NETFramework/v2.0/System.Data.OracleClient.dll", "build/.NETFramework/v2.0/System.Data.SqlXml.dll", "build/.NETFramework/v2.0/System.Data.dll", "build/.NETFramework/v2.0/System.Deployment.dll", "build/.NETFramework/v2.0/System.Design.dll", "build/.NETFramework/v2.0/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v2.0/System.DirectoryServices.dll", "build/.NETFramework/v2.0/System.Drawing.Design.dll", "build/.NETFramework/v2.0/System.Drawing.dll", "build/.NETFramework/v2.0/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v2.0/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v2.0/System.EnterpriseServices.dll", "build/.NETFramework/v2.0/System.Management.dll", "build/.NETFramework/v2.0/System.Messaging.dll", "build/.NETFramework/v2.0/System.Runtime.Remoting.dll", "build/.NETFramework/v2.0/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v2.0/System.Security.dll", "build/.NETFramework/v2.0/System.ServiceProcess.dll", "build/.NETFramework/v2.0/System.Transactions.dll", "build/.NETFramework/v2.0/System.Web.Mobile.dll", "build/.NETFramework/v2.0/System.Web.RegularExpressions.dll", "build/.NETFramework/v2.0/System.Web.Services.dll", "build/.NETFramework/v2.0/System.Web.dll", "build/.NETFramework/v2.0/System.Windows.Forms.dll", "build/.NETFramework/v2.0/System.Xml.dll", "build/.NETFramework/v2.0/System.dll", "build/.NETFramework/v2.0/cscompmgd.dll", "build/.NETFramework/v2.0/mscorlib.dll", "build/.NETFramework/v2.0/sysglobl.dll", "build/Microsoft.NETFramework.ReferenceAssemblies.net20.targets", "microsoft.netframework.referenceassemblies.net20.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.net20.nuspec"]}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj", "projectName": "DatabaseProjectAzureDevOps_WebAggrAI", "projectPath": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\DatabaseProjectAzureDevOps_WebAggrAI.sqlproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "s:\\DevOps\\DatabaseProjectAzureDevOps_WebAggrAI\\obj\\", "projectStyle": "PackageReference", "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1701", "NU5128"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "imports": ["netcoreapp1.0", "netcoreapp1.1", "netcoreapp2.0", "netcoreapp2.1", "netcoreapp2.2", "netcoreapp3.0", "netcoreapp3.1", "net5.0", "net6.0", "net7.0", "net8.0", "net9.0", "netstandard1.0", "netstandard1.1", "netstandard1.2", "netstandard1.3", "netstandard1.4", "netstandard1.5", "netstandard1.6", "netstandard2.0", "netstandard2.1", "net20", "net30", "net35", "net40", "net45", "net451", "net452", "net46", "net461", "net462", "net47", "net471", "net472", "net48", "net481", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}